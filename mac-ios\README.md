# macOS/iOS-Style Operating System

A browser-based desktop environment that mimics macOS/iOS using React, built with industry-standard architecture and deployable via Electron/Tauri.

![macOS Demo](https://img.shields.io/badge/React-18+-blue.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-Ready-blue.svg)
![Vite](https://img.shields.io/badge/Vite-5+-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## ✨ Features

### 🪟 Window Management
- **Draggable & Resizable Windows**: Full window manipulation with smooth animations
- **Minimize/Maximize/Close**: Complete window lifecycle management
- **Multi-Window Support**: Handle up to 50 windows simultaneously
- **Z-Index Management**: Proper window layering and focus handling

### 🖥️ Desktop Environment
- **Interactive Desktop**: Icon grid with context menus and wallpaper support
- **File Explorer**: Tree-view file system with mock CRUD operations
- **Responsive Design**: Adapts to mobile (iOS-like) and desktop layouts

### 🎨 Design System
- **macOS Monterey/iOS 16 Fidelity**: Authentic design recreation
- **Frosted Glass Effects**: Backdrop blur and transparency effects
- **SF Pro Font Family**: System font implementation
- **Light/Dark Themes**: Automatic system preference detection

### ⚡ Performance
- **≤100ms Interaction Latency**: Optimized for smooth interactions
- **React.memo Optimization**: Minimized re-renders
- **Virtualized Lists**: Efficient rendering for large datasets
- **Web Workers**: Offloaded file operations for better performance

### 📱 Touch Support
- **Virtual Keyboard**: On-screen keyboard for touch devices
- **Mobile Responsive**: iOS-like interface on mobile devices
- **Touch Gestures**: Native touch interaction support

## 🛠️ Technical Stack

### Core Technologies
- **React 18+** with Vite for development
- **TypeScript** for type safety
- **Styled Components** for CSS-in-JS styling
- **Zustand** for state management

### Testing & Quality
- **Vitest** + **React Testing Library** for testing
- **ESLint** + **Prettier** for code quality
- **Performance Monitoring** built-in for development

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mac-ios
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build

# Testing
npm run test         # Run tests
npm run test:ui      # Run tests with UI
npm run coverage     # Generate coverage report

# Code Quality
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run type-check   # Run TypeScript checks
```

## 🎮 Usage

### Basic Operations
- **Open Apps**: Click on desktop icons or dock items
- **Window Management**: Drag title bars to move, use window controls
- **File Explorer**: Navigate through the mock file system
- **Settings**: Customize themes and wallpapers
- **Virtual Keyboard**: Available on touch devices

### Keyboard Shortcuts
- `Escape`: Close virtual keyboard or deselect items
- `Cmd/Ctrl + A`: Select all (in file explorer)
- Click + `Cmd/Ctrl`: Multi-select items

### Performance Monitoring
In development mode, press "Show Perf" to view real-time performance metrics including:
- FPS counter
- Memory usage
- Render times
- Component counts

## 🧪 Testing

The project includes comprehensive test suites:

```bash
# Run all tests
npm run test

# Run specific test files
npm run test -- WindowFrame.test.jsx

# Run tests in watch mode
npm run test -- --watch

# Generate coverage report
npm run coverage
```

## 🎨 Customization

### Themes
Modify theme configurations in `src/store/themeStore.js`

### Apps
Add new applications by:
1. Creating app definition in `src/store/dockStore.js`
2. Adding window component in `src/features/window-manager/windows/`
3. Registering in `WindowManager.jsx`

## 📊 Performance Benchmarks

### Target Metrics
- **Window Rendering**: ≤16ms per frame
- **App Launch**: ≤100ms
- **File Operations**: ≤50ms
- **Memory Usage**: ≤100MB for 10 windows

## 🚀 Deployment

### Web Deployment
```bash
npm run build
# Deploy dist/ folder to your hosting service
```

### Desktop App (Electron)
```bash
npm install electron --save-dev
npm run build
npx electron-forge package
```

## 📝 License

This project is licensed under the MIT License.

---

**Built with ❤️ using React, TypeScript, and modern web technologies.**
