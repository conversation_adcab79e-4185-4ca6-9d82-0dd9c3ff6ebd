import { memo, useState, useEffect, useCallback, useMemo } from 'react';
import styled from 'styled-components';
import { VirtualScroller, performanceMonitor } from '../../utils/performance';

/**
 * Styled components for virtualized list
 */
const ListContainer = styled.div`
  height: ${props => props.height}px;
  overflow: auto;
  position: relative;
`;

const ListContent = styled.div`
  height: ${props => props.totalHeight}px;
  position: relative;
`;

const ListItem = styled.div`
  position: absolute;
  left: 0;
  right: 0;
  top: ${props => props.top}px;
  height: ${props => props.height}px;
  display: flex;
  align-items: center;
`;

/**
 * VirtualizedList component for rendering large lists efficiently
 * Only renders visible items to maintain performance
 * 
 * @param {Object} props
 * @param {Array} props.items - Array of items to render
 * @param {number} props.itemHeight - Height of each item in pixels
 * @param {number} props.height - Height of the container in pixels
 * @param {Function} props.renderItem - Function to render each item
 * @param {number} props.overscan - Number of items to render outside visible area
 * @param {string} props.className - CSS class name
 */
const VirtualizedList = memo(({
  items = [],
  itemHeight = 50,
  height = 400,
  renderItem,
  overscan = 5,
  className = ''
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  // Create virtual scroller instance
  const virtualScroller = useMemo(() => {
    return new VirtualScroller({
      itemHeight,
      containerHeight: height,
      overscan
    });
  }, [itemHeight, height, overscan]);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    performanceMonitor.startTiming('virtualList-calculate');
    const range = virtualScroller.getVisibleRange(items.length, scrollTop);
    performanceMonitor.endTiming('virtualList-calculate');
    return range;
  }, [virtualScroller, items.length, scrollTop]);

  // Get visible items
  const visibleItems = useMemo(() => {
    performanceMonitor.startTiming('virtualList-slice');
    const sliced = items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
    performanceMonitor.endTiming('virtualList-slice');
    return sliced;
  }, [items, visibleRange.startIndex, visibleRange.endIndex]);

  // Handle scroll events
  const handleScroll = useCallback((e) => {
    const newScrollTop = e.target.scrollTop;
    setScrollTop(newScrollTop);
  }, []);

  // Throttled scroll handler for better performance
  const throttledScrollHandler = useMemo(() => {
    let ticking = false;
    return (e) => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll(e);
          ticking = false;
        });
        ticking = true;
      }
    };
  }, [handleScroll]);

  return (
    <ListContainer
      height={height}
      onScroll={throttledScrollHandler}
      className={className}
    >
      <ListContent totalHeight={visibleRange.totalHeight}>
        {visibleItems.map((item, index) => {
          const actualIndex = visibleRange.startIndex + index;
          const top = actualIndex * itemHeight;
          
          return (
            <ListItem
              key={item.id || actualIndex}
              top={top}
              height={itemHeight}
            >
              {renderItem(item, actualIndex)}
            </ListItem>
          );
        })}
      </ListContent>
    </ListContainer>
  );
});

VirtualizedList.displayName = 'VirtualizedList';

export default VirtualizedList;
