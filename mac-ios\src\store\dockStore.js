import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import {
  RiFolderLine,
  RiGlobalLine,
  RiMessage3Line,
  RiMailLine,
  RiCameraLine,
  RiHeadphoneLine,
  RiStore2Line,
  RiStickyNoteLine,
  RiCalendarLine,
  RiSettings3Line,
  RiTerminalLine,
} from 'react-icons/ri';

/**
 * App definition interface
 */
export interface AppDefinition {
  id: string;
  name: string;
  icon: React.ComponentType;
  color: string;
  isRunning: boolean;
  category: 'system' | 'productivity' | 'entertainment' | 'utility';
}

/**
 * Dock store interface
 */
interface DockStore {
  apps: AppDefinition[];
  hoveredApp: string | null;
  
  // Actions
  setAppRunning: (appId: string, isRunning: boolean) => void;
  setHoveredApp: (appId: string | null) => void;
  getAppById: (appId: string) => AppDefinition | undefined;
  getRunningApps: () => AppDefinition[];
}

const defaultApps: AppDefinition[] = [
  { 
    id: 'finder', 
    name: 'Finder', 
    icon: RiFolderLine, 
    isRunning: true, 
    color: 'from-blue-400 to-blue-600',
    category: 'system'
  },
  { 
    id: 'safari', 
    name: 'Safari', 
    icon: RiGlobalLine, 
    isRunning: false, 
    color: 'from-blue-500 to-blue-700',
    category: 'productivity'
  },
  { 
    id: 'messages', 
    name: 'Messages', 
    icon: RiMessage3Line, 
    isRunning: false, 
    color: 'from-green-400 to-green-600',
    category: 'productivity'
  },
  { 
    id: 'mail', 
    name: 'Mail', 
    icon: RiMailLine, 
    isRunning: false, 
    color: 'from-blue-400 to-indigo-600',
    category: 'productivity'
  },
  { 
    id: 'photos', 
    name: 'Photos', 
    icon: RiCameraLine, 
    isRunning: false, 
    color: 'from-purple-400 to-purple-600',
    category: 'entertainment'
  },
  { 
    id: 'music', 
    name: 'Music', 
    icon: RiHeadphoneLine, 
    isRunning: false, 
    color: 'from-pink-400 to-pink-600',
    category: 'entertainment'
  },
  { 
    id: 'terminal', 
    name: 'Terminal', 
    icon: RiTerminalLine, 
    isRunning: false, 
    color: 'from-gray-700 to-gray-900',
    category: 'utility'
  },
  { 
    id: 'app-store', 
    name: 'App Store', 
    icon: RiStore2Line, 
    isRunning: false, 
    color: 'from-blue-400 to-blue-600',
    category: 'system'
  },
  { 
    id: 'notes', 
    name: 'Notes', 
    icon: RiStickyNoteLine, 
    isRunning: false, 
    color: 'from-yellow-400 to-yellow-600',
    category: 'productivity'
  },
  { 
    id: 'calendar', 
    name: 'Calendar', 
    icon: RiCalendarLine, 
    isRunning: false, 
    color: 'from-red-400 to-red-600',
    category: 'productivity'
  },
  { 
    id: 'settings', 
    name: 'Settings', 
    icon: RiSettings3Line, 
    isRunning: false, 
    color: 'from-gray-400 to-gray-600',
    category: 'system'
  },
];

const useDockStore = create<DockStore>()(
  subscribeWithSelector((set, get) => ({
    apps: defaultApps,
    hoveredApp: null,

    setAppRunning: (appId: string, isRunning: boolean) => {
      set(state => ({
        apps: state.apps.map(app =>
          app.id === appId ? { ...app, isRunning } : app
        )
      }));
    },

    setHoveredApp: (appId: string | null) => {
      set({ hoveredApp: appId });
    },

    getAppById: (appId: string) => {
      return get().apps.find(app => app.id === appId);
    },

    getRunningApps: () => {
      return get().apps.filter(app => app.isRunning);
    }
  }))
);

export default useDockStore;
