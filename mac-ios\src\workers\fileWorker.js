/**
 * Web Worker for file system operations
 * Handles heavy file operations off the main thread for better performance
 */

// File operation types
const FILE_OPERATIONS = {
  SEARCH: 'SEARCH',
  SORT: 'SORT',
  FILTER: 'FILTER',
  COMPRESS: 'COMPRESS',
  DECOMPRESS: 'DECOMPRESS',
  HASH: 'HASH',
  ANALYZE: 'ANALYZE'
};

/**
 * Search files by name, content, or metadata
 * @param {Array} files - Array of file objects
 * @param {string} query - Search query
 * @param {Object} options - Search options
 * @returns {Array} Filtered files
 */
function searchFiles(files, query, options = {}) {
  const { 
    searchInContent = false, 
    caseSensitive = false, 
    useRegex = false,
    fileTypes = []
  } = options;

  const searchQuery = caseSensitive ? query : query.toLowerCase();
  
  return files.filter(file => {
    // Filter by file type if specified
    if (fileTypes.length > 0) {
      const extension = file.name.split('.').pop()?.toLowerCase();
      if (!fileTypes.includes(extension)) return false;
    }

    const fileName = caseSensitive ? file.name : file.name.toLowerCase();
    
    // Search in file name
    if (useRegex) {
      try {
        const regex = new RegExp(searchQuery, caseSensitive ? 'g' : 'gi');
        if (regex.test(fileName)) return true;
      } catch (e) {
        // Fallback to simple search if regex is invalid
        if (fileName.includes(searchQuery)) return true;
      }
    } else {
      if (fileName.includes(searchQuery)) return true;
    }

    // Search in file content if enabled and content exists
    if (searchInContent && file.content) {
      const content = caseSensitive ? file.content : file.content.toLowerCase();
      if (useRegex) {
        try {
          const regex = new RegExp(searchQuery, caseSensitive ? 'g' : 'gi');
          if (regex.test(content)) return true;
        } catch (e) {
          if (content.includes(searchQuery)) return true;
        }
      } else {
        if (content.includes(searchQuery)) return true;
      }
    }

    return false;
  });
}

/**
 * Sort files by various criteria
 * @param {Array} files - Array of file objects
 * @param {string} sortBy - Sort criteria (name, size, modified, type)
 * @param {string} order - Sort order (asc, desc)
 * @returns {Array} Sorted files
 */
function sortFiles(files, sortBy = 'name', order = 'asc') {
  const sortedFiles = [...files].sort((a, b) => {
    let aValue, bValue;

    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case 'size':
        aValue = parseFloat(a.size?.replace(/[^\d.]/g, '') || '0');
        bValue = parseFloat(b.size?.replace(/[^\d.]/g, '') || '0');
        break;
      case 'modified':
        aValue = new Date(a.modified || 0);
        bValue = new Date(b.modified || 0);
        break;
      case 'type':
        aValue = a.type;
        bValue = b.type;
        break;
      default:
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
    }

    if (aValue < bValue) return order === 'asc' ? -1 : 1;
    if (aValue > bValue) return order === 'asc' ? 1 : -1;
    return 0;
  });

  // Always put folders first
  return sortedFiles.sort((a, b) => {
    if (a.type === 'folder' && b.type !== 'folder') return -1;
    if (a.type !== 'folder' && b.type === 'folder') return 1;
    return 0;
  });
}

/**
 * Filter files by various criteria
 * @param {Array} files - Array of file objects
 * @param {Object} filters - Filter criteria
 * @returns {Array} Filtered files
 */
function filterFiles(files, filters) {
  const {
    types = [],
    sizeRange = null,
    dateRange = null,
    showHidden = true
  } = filters;

  return files.filter(file => {
    // Filter by type
    if (types.length > 0 && !types.includes(file.type)) {
      return false;
    }

    // Filter by size
    if (sizeRange) {
      const fileSize = parseFloat(file.size?.replace(/[^\d.]/g, '') || '0');
      if (fileSize < sizeRange.min || fileSize > sizeRange.max) {
        return false;
      }
    }

    // Filter by date
    if (dateRange) {
      const fileDate = new Date(file.modified || 0);
      if (fileDate < dateRange.start || fileDate > dateRange.end) {
        return false;
      }
    }

    // Filter hidden files
    if (!showHidden && file.name.startsWith('.')) {
      return false;
    }

    return true;
  });
}

/**
 * Simple hash function for file content
 * @param {string} content - File content
 * @returns {string} Hash string
 */
function simpleHash(content) {
  let hash = 0;
  if (content.length === 0) return hash.toString();
  
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString(16);
}

/**
 * Analyze file content and extract metadata
 * @param {Object} file - File object
 * @returns {Object} Analysis results
 */
function analyzeFile(file) {
  const analysis = {
    wordCount: 0,
    lineCount: 0,
    characterCount: 0,
    hash: null,
    encoding: 'utf-8',
    language: null
  };

  if (!file.content) return analysis;

  const content = file.content;
  analysis.characterCount = content.length;
  analysis.lineCount = content.split('\n').length;
  analysis.wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
  analysis.hash = simpleHash(content);

  // Simple language detection based on file extension
  const extension = file.name.split('.').pop()?.toLowerCase();
  const languageMap = {
    'js': 'javascript',
    'jsx': 'javascript',
    'ts': 'typescript',
    'tsx': 'typescript',
    'py': 'python',
    'java': 'java',
    'cpp': 'cpp',
    'c': 'c',
    'html': 'html',
    'css': 'css',
    'json': 'json',
    'xml': 'xml',
    'md': 'markdown',
    'txt': 'text'
  };

  analysis.language = languageMap[extension] || 'unknown';

  return analysis;
}

/**
 * Simulate file compression
 * @param {string} content - File content
 * @returns {Object} Compression result
 */
function compressContent(content) {
  // Simple compression simulation using repetition detection
  const compressed = content.replace(/(.)\1{2,}/g, (match, char) => {
    return `${char}[${match.length}]`;
  });

  return {
    original: content,
    compressed: compressed,
    originalSize: content.length,
    compressedSize: compressed.length,
    ratio: compressed.length / content.length
  };
}

/**
 * Message handler for the web worker
 */
self.onmessage = function(e) {
  const { id, operation, data } = e.data;

  try {
    let result;

    switch (operation) {
      case FILE_OPERATIONS.SEARCH:
        result = searchFiles(data.files, data.query, data.options);
        break;

      case FILE_OPERATIONS.SORT:
        result = sortFiles(data.files, data.sortBy, data.order);
        break;

      case FILE_OPERATIONS.FILTER:
        result = filterFiles(data.files, data.filters);
        break;

      case FILE_OPERATIONS.COMPRESS:
        result = compressContent(data.content);
        break;

      case FILE_OPERATIONS.HASH:
        result = simpleHash(data.content);
        break;

      case FILE_OPERATIONS.ANALYZE:
        result = analyzeFile(data.file);
        break;

      default:
        throw new Error(`Unknown operation: ${operation}`);
    }

    // Send success response
    self.postMessage({
      id,
      success: true,
      result,
      operation
    });

  } catch (error) {
    // Send error response
    self.postMessage({
      id,
      success: false,
      error: error.message,
      operation
    });
  }
};

// Export operation types for use in main thread
self.FILE_OPERATIONS = FILE_OPERATIONS;
