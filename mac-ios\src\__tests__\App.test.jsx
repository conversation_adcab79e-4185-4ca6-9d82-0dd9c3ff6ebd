import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import App from '../App';

/**
 * Test suite for the main App component
 * Tests the overall application structure and theme integration
 */
describe('App Component', () => {
  beforeEach(() => {
    // Reset any global state before each test
    localStorage.clear();
  });

  it('renders without crashing', () => {
    render(<App />);
    expect(document.body).toBeTruthy();
  });

  it('applies theme provider correctly', () => {
    render(<App />);
    
    // Check if the app has the expected structure
    const appContainer = document.querySelector('body');
    expect(appContainer).toBeTruthy();
  });

  it('renders desktop component', () => {
    render(<App />);
    
    // Desktop should be present (though it might not have specific test IDs yet)
    // This is a basic smoke test to ensure components render
    expect(document.body.children.length).toBeGreaterThan(0);
  });

  it('renders dock component', () => {
    render(<App />);
    
    // Dock should be present in the DOM
    expect(document.body.children.length).toBeGreaterThan(0);
  });

  it('renders window manager', () => {
    render(<App />);
    
    // Window manager should be present
    expect(document.body.children.length).toBeGreaterThan(0);
  });
});
