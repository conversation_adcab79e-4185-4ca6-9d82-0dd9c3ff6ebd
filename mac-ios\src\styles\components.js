import styled, { css } from 'styled-components';

/**
 * Reusable styled components for the macOS/iOS operating system
 * These components implement the design system and can be used throughout the app
 */

// Glass effect mixin
export const glassEffect = css`
  background: ${props => props.theme.colors.glass};
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

export const strongGlassEffect = css`
  background: ${props => props.theme.colors.glassStrong};
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  border: 1px solid rgba(255, 255, 255, 0.3);
`;

// Button variants
export const Button = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.md};
  font-family: ${props => props.theme.fonts.system};
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;
  outline: none;

  &:focus-visible {
    outline: 2px solid ${props => props.theme.colors.primary};
    outline-offset: 2px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  ${props => props.variant === 'primary' && css`
    background: ${props => props.theme.colors.primary};
    color: white;

    &:hover:not(:disabled) {
      background: ${props => props.theme.colors.primary}dd;
    }

    &:active:not(:disabled) {
      background: ${props => props.theme.colors.primary}bb;
    }
  `}

  ${props => props.variant === 'secondary' && css`
    background: ${props => props.theme.colors.surface};
    color: ${props => props.theme.colors.text};

    &:hover:not(:disabled) {
      background: ${props => props.theme.colors.surface}dd;
    }

    &:active:not(:disabled) {
      background: ${props => props.theme.colors.surface}bb;
    }
  `}

  ${props => props.variant === 'ghost' && css`
    background: transparent;
    color: ${props => props.theme.colors.text};

    &:hover:not(:disabled) {
      background: ${props => props.theme.colors.surface}44;
    }

    &:active:not(:disabled) {
      background: ${props => props.theme.colors.surface}66;
    }
  `}

  ${props => props.size === 'sm' && css`
    padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
    font-size: 0.75rem;
  `}

  ${props => props.size === 'lg' && css`
    padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
    font-size: 1rem;
  `}
`;

// Input component
export const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.md};
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  border: 1px solid ${props => props.theme.colors.border};
  font-family: ${props => props.theme.fonts.system};
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;

  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}22;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

// Card component
export const Card = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  box-shadow: ${props => props.theme.shadows.md};
  border: 1px solid ${props => props.theme.colors.border};

  ${props => props.glass && glassEffect}
  ${props => props.strongGlass && strongGlassEffect}
`;

// Icon button for window controls
export const IconButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: transparent;
  color: ${props => props.theme.colors.textSecondary};
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;

  &:hover {
    background: ${props => props.theme.colors.surface};
    color: ${props => props.theme.colors.text};
  }

  &:active {
    background: ${props => props.theme.colors.surface}dd;
  }

  &:focus-visible {
    outline: 2px solid ${props => props.theme.colors.primary};
    outline-offset: 2px;
  }

  ${props => props.variant === 'close' && css`
    background: #ff5f57;
    color: white;

    &:hover {
      background: #ff4136;
    }
  `}

  ${props => props.variant === 'minimize' && css`
    background: #ffbd2e;
    color: white;

    &:hover {
      background: #ff9500;
    }
  `}

  ${props => props.variant === 'maximize' && css`
    background: #28ca42;
    color: white;

    &:hover {
      background: #34c759;
    }
  `}
`;

// Flex utilities
export const Flex = styled.div`
  display: flex;
  align-items: ${props => props.align || 'stretch'};
  justify-content: ${props => props.justify || 'flex-start'};
  flex-direction: ${props => props.direction || 'row'};
  gap: ${props => props.gap ? props.theme.spacing[props.gap] || props.gap : '0'};
  flex-wrap: ${props => props.wrap ? 'wrap' : 'nowrap'};
`;

// Grid utilities
export const Grid = styled.div`
  display: grid;
  grid-template-columns: ${props => props.cols || 'repeat(auto-fit, minmax(250px, 1fr))'};
  grid-template-rows: ${props => props.rows || 'auto'};
  gap: ${props => props.gap ? props.theme.spacing[props.gap] || props.gap : props.theme.spacing.md};
  align-items: ${props => props.align || 'stretch'};
  justify-items: ${props => props.justify || 'stretch'};
`;

// Text components
export const Text = styled.span`
  font-family: ${props => props.theme.fonts.system};
  color: ${props => props.theme.colors.text};
  font-size: ${props => {
    switch (props.size) {
      case 'xs': return '0.75rem';
      case 'sm': return '0.875rem';
      case 'lg': return '1.125rem';
      case 'xl': return '1.25rem';
      case '2xl': return '1.5rem';
      default: return '1rem';
    }
  }};
  font-weight: ${props => props.weight || 400};
  line-height: ${props => props.lineHeight || 1.5};

  ${props => props.secondary && css`
    color: ${props => props.theme.colors.textSecondary};
  `}

  ${props => props.mono && css`
    font-family: ${props => props.theme.fonts.mono};
  `}
`;

// Container for layout
export const Container = styled.div`
  width: 100%;
  max-width: ${props => props.maxWidth || 'none'};
  margin: 0 auto;
  padding: 0 ${props => props.theme.spacing.md};

  @media (max-width: 768px) {
    padding: 0 ${props => props.theme.spacing.sm};
  }
`;

// Backdrop for modals and overlays
export const Backdrop = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s ease-out;
`;
