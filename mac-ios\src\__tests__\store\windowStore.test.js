import { describe, it, expect, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import useWindowStore from '../../store/windowStore';

/**
 * Test suite for the window store
 * Tests window management functionality
 */
describe('Window Store', () => {
  beforeEach(() => {
    // Reset store state before each test
    const { result } = renderHook(() => useWindowStore());
    act(() => {
      // Clear all windows
      result.current.windows.forEach(window => {
        result.current.closeWindow(window.id);
      });
    });
  });

  it('should initialize with empty windows array', () => {
    const { result } = renderHook(() => useWindowStore());
    expect(result.current.windows).toEqual([]);
    expect(result.current.activeWindow).toBeNull();
    expect(result.current.maxZIndex).toBe(1);
  });

  it('should open a new window', () => {
    const { result } = renderHook(() => useWindowStore());
    
    act(() => {
      result.current.openWindow('test-app', 'Test App');
    });

    expect(result.current.windows).toHaveLength(1);
    expect(result.current.windows[0].title).toBe('Test App');
    expect(result.current.windows[0].appId).toBe('test-app');
    expect(result.current.activeWindow).toBe(result.current.windows[0].id);
  });

  it('should not create duplicate windows for same app', () => {
    const { result } = renderHook(() => useWindowStore());
    
    act(() => {
      result.current.openWindow('test-app', 'Test App');
      result.current.openWindow('test-app', 'Test App');
    });

    expect(result.current.windows).toHaveLength(1);
  });

  it('should close a window', () => {
    const { result } = renderHook(() => useWindowStore());
    
    act(() => {
      result.current.openWindow('test-app', 'Test App');
    });

    const windowId = result.current.windows[0].id;

    act(() => {
      result.current.closeWindow(windowId);
    });

    expect(result.current.windows).toHaveLength(0);
    expect(result.current.activeWindow).toBeNull();
  });

  it('should minimize a window', () => {
    const { result } = renderHook(() => useWindowStore());
    
    act(() => {
      result.current.openWindow('test-app', 'Test App');
    });

    const windowId = result.current.windows[0].id;

    act(() => {
      result.current.minimizeWindow(windowId);
    });

    expect(result.current.windows[0].minimized).toBe(true);
    expect(result.current.activeWindow).toBeNull();
  });

  it('should maximize and restore a window', () => {
    const { result } = renderHook(() => useWindowStore());
    
    act(() => {
      result.current.openWindow('test-app', 'Test App');
    });

    const windowId = result.current.windows[0].id;
    const originalSize = result.current.windows[0].size;

    act(() => {
      result.current.maximizeWindow(windowId);
    });

    expect(result.current.windows[0].maximized).toBe(true);
    expect(result.current.windows[0].prevSize).toEqual(originalSize);

    act(() => {
      result.current.maximizeWindow(windowId); // Toggle back
    });

    expect(result.current.windows[0].maximized).toBe(false);
    expect(result.current.windows[0].size).toEqual(originalSize);
  });

  it('should bring window to front', () => {
    const { result } = renderHook(() => useWindowStore());
    
    act(() => {
      result.current.openWindow('app1', 'App 1');
      result.current.openWindow('app2', 'App 2');
    });

    const firstWindowId = result.current.windows[0].id;
    const secondWindowId = result.current.windows[1].id;

    expect(result.current.activeWindow).toBe(secondWindowId);

    act(() => {
      result.current.bringToFront(firstWindowId);
    });

    expect(result.current.activeWindow).toBe(firstWindowId);
    expect(result.current.windows.find(w => w.id === firstWindowId).zIndex)
      .toBeGreaterThan(result.current.windows.find(w => w.id === secondWindowId).zIndex);
  });

  it('should update window position', () => {
    const { result } = renderHook(() => useWindowStore());
    
    act(() => {
      result.current.openWindow('test-app', 'Test App');
    });

    const windowId = result.current.windows[0].id;
    const newPosition = { x: 200, y: 300 };

    act(() => {
      result.current.updateWindowPosition(windowId, newPosition);
    });

    expect(result.current.windows[0].position).toEqual(newPosition);
  });

  it('should handle dragging state', () => {
    const { result } = renderHook(() => useWindowStore());
    
    expect(result.current.isDragging).toBe(false);
    expect(result.current.draggedWindow).toBeNull();

    act(() => {
      result.current.setDragging(true, 'window-id');
    });

    expect(result.current.isDragging).toBe(true);
    expect(result.current.draggedWindow).toBe('window-id');

    act(() => {
      result.current.setDragging(false);
    });

    expect(result.current.isDragging).toBe(false);
    expect(result.current.draggedWindow).toBeNull();
  });
});
