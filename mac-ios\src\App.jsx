import { useState } from 'react';
import { ThemeProvider } from 'styled-components';
import { useThemeStore } from './store';
import { GlobalStyles } from './styles/GlobalStyles';
import Desktop from './features/desktop/Desktop';
import WindowManager from './features/window-manager/WindowManager';
import Dock from './features/dock/components/Dock';
import VirtualKeyboard from './features/virtual-keyboard/VirtualKeyboard';
import PerformanceMonitor from './components/performance/PerformanceMonitor';
import 'remixicon/fonts/remixicon.css';

/**
 * Main App component that provides the macOS/iOS-style operating system
 * Uses ThemeProvider to provide theme context to all components
 */
const App = () => {
  const { getCurrentTheme } = useThemeStore();
  const theme = getCurrentTheme();
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Enable performance monitoring in development
  const isDevelopment = import.meta.env.DEV;

  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles />
      <Desktop />
      <WindowManager />
      <Dock />
      <VirtualKeyboard
        visible={keyboardVisible}
        onClose={() => setKeyboardVisible(false)}
      />
      <PerformanceMonitor enabled={isDevelopment} />
    </ThemeProvider>
  );
};



export default App;