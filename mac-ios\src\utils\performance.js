/**
 * Performance utilities for the macOS/iOS operating system
 * Provides optimization helpers and monitoring tools
 */

/**
 * Debounce function to limit the rate of function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @param {boolean} immediate - Whether to execute immediately
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait, immediate = false) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
};

/**
 * Throttle function to limit function execution frequency
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} Throttled function
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Request animation frame with fallback
 * @param {Function} callback - Callback function
 * @returns {number} Request ID
 */
export const requestAnimationFrame = (callback) => {
  return window.requestAnimationFrame || 
         window.webkitRequestAnimationFrame || 
         window.mozRequestAnimationFrame || 
         window.oRequestAnimationFrame || 
         window.msRequestAnimationFrame || 
         function(callback) {
           window.setTimeout(callback, 1000 / 60);
         };
};

/**
 * Cancel animation frame with fallback
 * @param {number} id - Request ID to cancel
 */
export const cancelAnimationFrame = (id) => {
  const cancel = window.cancelAnimationFrame || 
                 window.webkitCancelAnimationFrame || 
                 window.mozCancelAnimationFrame || 
                 window.oCancelAnimationFrame || 
                 window.msCancelAnimationFrame || 
                 clearTimeout;
  cancel(id);
};

/**
 * Performance monitor class for tracking metrics
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = new Map();
  }

  /**
   * Start timing a performance metric
   * @param {string} name - Metric name
   */
  startTiming(name) {
    this.metrics.set(name, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    });
  }

  /**
   * End timing a performance metric
   * @param {string} name - Metric name
   * @returns {number} Duration in milliseconds
   */
  endTiming(name) {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" not found`);
      return 0;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;
    
    // Log if duration exceeds 100ms threshold
    if (metric.duration > 100) {
      console.warn(`Performance warning: "${name}" took ${metric.duration.toFixed(2)}ms`);
    }

    return metric.duration;
  }

  /**
   * Get metric duration
   * @param {string} name - Metric name
   * @returns {number|null} Duration in milliseconds
   */
  getMetric(name) {
    const metric = this.metrics.get(name);
    return metric ? metric.duration : null;
  }

  /**
   * Clear all metrics
   */
  clearMetrics() {
    this.metrics.clear();
  }

  /**
   * Observe element performance using Intersection Observer
   * @param {Element} element - Element to observe
   * @param {Function} callback - Callback function
   */
  observeElement(element, callback) {
    if (!window.IntersectionObserver) return;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        callback(entry);
      });
    }, {
      threshold: [0, 0.25, 0.5, 0.75, 1]
    });

    observer.observe(element);
    this.observers.set(element, observer);
  }

  /**
   * Stop observing element
   * @param {Element} element - Element to stop observing
   */
  unobserveElement(element) {
    const observer = this.observers.get(element);
    if (observer) {
      observer.unobserve(element);
      this.observers.delete(element);
    }
  }

  /**
   * Disconnect all observers
   */
  disconnectAll() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }
}

/**
 * Global performance monitor instance
 */
export const performanceMonitor = new PerformanceMonitor();

/**
 * Virtual scrolling utility for large lists
 */
export class VirtualScroller {
  constructor(options = {}) {
    this.itemHeight = options.itemHeight || 50;
    this.containerHeight = options.containerHeight || 400;
    this.overscan = options.overscan || 5;
    this.scrollTop = 0;
  }

  /**
   * Calculate visible items based on scroll position
   * @param {number} totalItems - Total number of items
   * @param {number} scrollTop - Current scroll position
   * @returns {Object} Visible range and offset
   */
  getVisibleRange(totalItems, scrollTop = this.scrollTop) {
    this.scrollTop = scrollTop;
    
    const startIndex = Math.floor(scrollTop / this.itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(this.containerHeight / this.itemHeight),
      totalItems - 1
    );

    const visibleStartIndex = Math.max(0, startIndex - this.overscan);
    const visibleEndIndex = Math.min(totalItems - 1, endIndex + this.overscan);

    return {
      startIndex: visibleStartIndex,
      endIndex: visibleEndIndex,
      offsetY: visibleStartIndex * this.itemHeight,
      totalHeight: totalItems * this.itemHeight
    };
  }
}

/**
 * Memory management utilities
 */
export const memoryUtils = {
  /**
   * Clean up event listeners
   * @param {Element} element - Element to clean up
   */
  cleanupEventListeners(element) {
    if (element && element.cloneNode) {
      const newElement = element.cloneNode(true);
      element.parentNode?.replaceChild(newElement, element);
      return newElement;
    }
    return element;
  },

  /**
   * Force garbage collection (if available)
   */
  forceGC() {
    if (window.gc) {
      window.gc();
    }
  },

  /**
   * Get memory usage information
   * @returns {Object|null} Memory info or null if not available
   */
  getMemoryInfo() {
    if (performance.memory) {
      return {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
        usedPercentage: (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100
      };
    }
    return null;
  }
};

/**
 * Image optimization utilities
 */
export const imageUtils = {
  /**
   * Lazy load image with intersection observer
   * @param {HTMLImageElement} img - Image element
   * @param {string} src - Image source URL
   * @param {Function} onLoad - Load callback
   */
  lazyLoadImage(img, src, onLoad) {
    if (!window.IntersectionObserver) {
      img.src = src;
      if (onLoad) onLoad();
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          img.src = src;
          img.onload = onLoad;
          observer.unobserve(img);
        }
      });
    });

    observer.observe(img);
  },

  /**
   * Preload images
   * @param {string[]} urls - Array of image URLs
   * @returns {Promise} Promise that resolves when all images are loaded
   */
  preloadImages(urls) {
    const promises = urls.map(url => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = resolve;
        img.onerror = reject;
        img.src = url;
      });
    });

    return Promise.all(promises);
  }
};

/**
 * Animation utilities for smooth performance
 */
export const animationUtils = {
  /**
   * Smooth scroll to element
   * @param {Element} element - Target element
   * @param {Object} options - Scroll options
   */
  smoothScrollTo(element, options = {}) {
    const { duration = 300, easing = 'easeInOutQuad' } = options;
    
    const start = window.pageYOffset;
    const target = element.offsetTop;
    const distance = target - start;
    const startTime = performance.now();

    const easingFunctions = {
      easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
      easeOutCubic: t => (--t) * t * t + 1,
      easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
    };

    const easingFunction = easingFunctions[easing] || easingFunctions.easeInOutQuad;

    function scroll() {
      const elapsed = performance.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const ease = easingFunction(progress);
      
      window.scrollTo(0, start + distance * ease);
      
      if (progress < 1) {
        requestAnimationFrame(scroll);
      }
    }

    requestAnimationFrame(scroll);
  },

  /**
   * Create optimized animation loop
   * @param {Function} callback - Animation callback
   * @returns {Function} Stop function
   */
  createAnimationLoop(callback) {
    let isRunning = true;
    let lastTime = 0;

    function animate(currentTime) {
      if (!isRunning) return;

      const deltaTime = currentTime - lastTime;
      lastTime = currentTime;

      callback(deltaTime, currentTime);
      requestAnimationFrame(animate);
    }

    requestAnimationFrame(animate);

    return () => {
      isRunning = false;
    };
  }
};
