import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  RiArrowLeftSLine, 
  RiArrowRightSLine,
  RiRefreshLine,
  RiHomeLine,
  RiBookmarkLine,
  RiShareLine,
  RiSearchLine
} from 'react-icons/ri';
import { Input, Button, Flex, Text } from '../../../styles/components';

/**
 * Styled components for the Safari window
 */
const SafariContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  background: ${props => props.theme.colors.background};
`;

const SafariToolbar = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  gap: ${props => props.theme.spacing.sm};
`;

const AddressBar = styled.div`
  position: relative;
  flex: 1;
  max-width: 600px;
  margin: 0 ${props => props.theme.spacing.md};
`;

const AddressInput = styled(Input)`
  padding-left: 2.5rem;
  border-radius: 1.5rem;
  background: ${props => props.theme.colors.background};
`;

const AddressIcon = styled(RiSearchLine)`
  position: absolute;
  left: ${props => props.theme.spacing.sm};
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.textSecondary};
  pointer-events: none;
`;

const WebContent = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.xl};
  overflow: auto;
  background: ${props => props.theme.colors.background};
`;

const WebPage = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const PageTitle = styled.h1`
  font-size: 2rem;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.lg};
  text-align: center;
`;

const PageSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const Paragraph = styled.p`
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.6;
  margin-bottom: ${props => props.theme.spacing.md};
`;

const FeatureGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${props => props.theme.spacing.lg};
  margin: ${props => props.theme.spacing.xl} 0;
`;

const FeatureCard = styled.div`
  padding: ${props => props.theme.spacing.lg};
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  border: 1px solid ${props => props.theme.colors.border};
`;

const FeatureTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const FeatureDescription = styled.p`
  color: ${props => props.theme.colors.textSecondary};
  font-size: 0.875rem;
  line-height: 1.5;
`;

/**
 * Safari window content component
 * Simulates a web browser with a demo webpage
 * 
 * @param {Object} props
 * @param {Object} props.window - Window state object
 */
const SafariWindow = ({ window }) => {
  const [url, setUrl] = useState('https://macos-demo.local');
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);

  const handleUrlSubmit = (e) => {
    e.preventDefault();
    // In a real browser, this would navigate to the URL
    console.log('Navigating to:', url);
  };

  return (
    <SafariContainer>
      <SafariToolbar>
        <Button 
          variant="ghost" 
          size="sm" 
          disabled={!canGoBack}
        >
          <RiArrowLeftSLine />
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          disabled={!canGoForward}
        >
          <RiArrowRightSLine />
        </Button>
        <Button variant="ghost" size="sm">
          <RiRefreshLine />
        </Button>

        <AddressBar>
          <form onSubmit={handleUrlSubmit}>
            <AddressInput
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="Search or enter website name"
            />
            <AddressIcon />
          </form>
        </AddressBar>

        <Button variant="ghost" size="sm">
          <RiHomeLine />
        </Button>
        <Button variant="ghost" size="sm">
          <RiBookmarkLine />
        </Button>
        <Button variant="ghost" size="sm">
          <RiShareLine />
        </Button>
      </SafariToolbar>

      <WebContent>
        <WebPage>
          <PageTitle>Welcome to macOS Demo</PageTitle>
          
          <PageSection>
            <Paragraph>
              This is a demonstration of a macOS-style operating system built with React. 
              You're currently viewing this page in a simulated Safari browser window.
            </Paragraph>
          </PageSection>

          <PageSection>
            <SectionTitle>Features</SectionTitle>
            <FeatureGrid>
              <FeatureCard>
                <FeatureTitle>Window Management</FeatureTitle>
                <FeatureDescription>
                  Drag, resize, minimize, and maximize windows just like in macOS. 
                  Multiple windows can be open simultaneously.
                </FeatureDescription>
              </FeatureCard>
              
              <FeatureCard>
                <FeatureTitle>Desktop Environment</FeatureTitle>
                <FeatureDescription>
                  Interactive desktop with app icons, wallpaper support, and 
                  context menus for a complete desktop experience.
                </FeatureDescription>
              </FeatureCard>
              
              <FeatureCard>
                <FeatureTitle>Dock System</FeatureTitle>
                <FeatureDescription>
                  macOS-style dock with app launchers, running app indicators, 
                  and smooth hover animations.
                </FeatureDescription>
              </FeatureCard>
              
              <FeatureCard>
                <FeatureTitle>Theme Support</FeatureTitle>
                <FeatureDescription>
                  Light and dark themes with automatic system preference detection 
                  and smooth transitions between modes.
                </FeatureDescription>
              </FeatureCard>
            </FeatureGrid>
          </PageSection>

          <PageSection>
            <SectionTitle>Technology Stack</SectionTitle>
            <Paragraph>
              Built with React 18+, Styled Components for styling, Zustand for state management, 
              and Vite for development. The design closely follows macOS Monterey guidelines 
              with frosted glass effects and SF Pro typography.
            </Paragraph>
          </PageSection>

          <PageSection>
            <SectionTitle>Try It Out</SectionTitle>
            <Paragraph>
              Try opening multiple windows, dragging them around, and exploring the different 
              applications available in the dock. The system is fully interactive and 
              responsive across different screen sizes.
            </Paragraph>
          </PageSection>
        </WebPage>
      </WebContent>
    </SafariContainer>
  );
};

export default SafariWindow;
