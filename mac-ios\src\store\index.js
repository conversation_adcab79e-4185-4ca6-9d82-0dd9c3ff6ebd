/**
 * Central store exports for the macOS/iOS operating system
 * 
 * This file provides a single point of import for all Zustand stores
 * used throughout the application.
 */

export { default as useWindowStore } from './windowStore.js';
export { default as useDockStore } from './dockStore.js';
export { default as useDesktopStore } from './desktopStore.js';
export { default as useThemeStore } from './themeStore.js';
export { default as useFileSystemStore } from '../features/file-explorer/store/fileSystemStore.js';

// Types are exported from individual store files
// Import them directly from the store files when needed in TypeScript
