import React from 'react';
import styled from 'styled-components';
import { useWindowStore } from '../../store';
import WindowFrame from '../../components/window/WindowFrame';
import FinderWindow from './windows/FinderWindow';
import SafariWindow from './windows/SafariWindow';
import SettingsWindow from './windows/SettingsWindow';
import DefaultWindow from './windows/DefaultWindow';
import FileExplorer from '../file-explorer/FileExplorer';

/**
 * Styled components for the window manager
 */
const WindowManagerContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 100;
`;

/**
 * Window content components mapping
 */
const windowComponents = {
  finder: FileExplorer,
  safari: SafariWindow,
  settings: SettingsWindow,
  // Add more window types as needed
};

/**
 * WindowManager component that renders all active windows
 * Handles window lifecycle and content rendering
 */
const WindowManager = () => {
  const { windows } = useWindowStore();

  // Filter out minimized windows (they're handled by the dock)
  const visibleWindows = windows.filter(window => !window.minimized);

  const renderWindowContent = (window) => {
    const WindowComponent = windowComponents[window.appId] || DefaultWindow;
    return <WindowComponent window={window} />;
  };

  return (
    <WindowManagerContainer>
      {visibleWindows.map(window => (
        <WindowFrame key={window.id} windowId={window.id}>
          {renderWindowContent(window)}
        </WindowFrame>
      ))}
    </WindowManagerContainer>
  );
};

export default WindowManager;
