import { useEffect, useRef, useState } from 'react';

/**
 * Custom hook for making elements draggable
 * 
 * @param {Object} options - Configuration options
 * @param {React.RefObject} options.elementRef - Ref to the draggable element
 * @param {React.RefObject} options.handleRef - Ref to the drag handle (optional, defaults to elementRef)
 * @param {Function} options.onDragStart - Callback when drag starts
 * @param {Function} options.onDrag - Callback during drag with position
 * @param {Function} options.onDragEnd - Callback when drag ends
 * @param {boolean} options.disabled - Whether dragging is disabled
 * @param {Object} options.bounds - Boundary constraints for dragging
 * @returns {Object} - Object containing isDragging state
 */
export const useDraggable = ({
  elementRef,
  handleRef,
  onDragStart,
  onDrag,
  onDragEnd,
  disabled = false,
  bounds = null
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const dragOffset = useRef({ x: 0, y: 0 });
  const initialPosition = useRef({ x: 0, y: 0 });

  useEffect(() => {
    if (disabled || !elementRef.current) return;

    const element = elementRef.current;
    const handle = handleRef?.current || element;

    const handleMouseDown = (e) => {
      // Only handle left mouse button
      if (e.button !== 0) return;

      e.preventDefault();
      e.stopPropagation();

      const rect = element.getBoundingClientRect();
      
      // Calculate offset from mouse to element's top-left corner
      dragOffset.current = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      };

      // Store initial position
      initialPosition.current = {
        x: rect.left,
        y: rect.top
      };

      setIsDragging(true);
      
      if (onDragStart) {
        onDragStart({ x: rect.left, y: rect.top });
      }

      // Add global event listeners
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      // Prevent text selection during drag
      document.body.style.userSelect = 'none';
    };

    const handleMouseMove = (e) => {
      if (!isDragging && !dragOffset.current) return;

      e.preventDefault();

      // Calculate new position
      let newX = e.clientX - dragOffset.current.x;
      let newY = e.clientY - dragOffset.current.y;

      // Apply bounds constraints if provided
      if (bounds) {
        const elementRect = element.getBoundingClientRect();
        const elementWidth = elementRect.width;
        const elementHeight = elementRect.height;

        if (bounds.left !== undefined) {
          newX = Math.max(bounds.left, newX);
        }
        if (bounds.right !== undefined) {
          newX = Math.min(bounds.right - elementWidth, newX);
        }
        if (bounds.top !== undefined) {
          newY = Math.max(bounds.top, newY);
        }
        if (bounds.bottom !== undefined) {
          newY = Math.min(bounds.bottom - elementHeight, newY);
        }
      } else {
        // Default bounds to keep window partially visible
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const elementRect = element.getBoundingClientRect();
        const minVisibleSize = 100; // Minimum visible area

        newX = Math.max(-elementRect.width + minVisibleSize, newX);
        newX = Math.min(viewportWidth - minVisibleSize, newX);
        newY = Math.max(0, newY); // Don't allow dragging above viewport
        newY = Math.min(viewportHeight - minVisibleSize, newY);
      }

      if (onDrag) {
        onDrag({ x: newX, y: newY });
      }
    };

    const handleMouseUp = (e) => {
      e.preventDefault();
      
      setIsDragging(false);
      dragOffset.current = { x: 0, y: 0 };

      // Remove global event listeners
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      
      // Restore text selection
      document.body.style.userSelect = '';

      if (onDragEnd) {
        const rect = element.getBoundingClientRect();
        onDragEnd({ x: rect.left, y: rect.top });
      }
    };

    // Add mousedown listener to handle
    handle.addEventListener('mousedown', handleMouseDown);

    // Cleanup function
    return () => {
      handle.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [disabled, elementRef, handleRef, onDragStart, onDrag, onDragEnd, bounds, isDragging]);

  // Handle touch events for mobile support
  useEffect(() => {
    if (disabled || !elementRef.current) return;

    const element = elementRef.current;
    const handle = handleRef?.current || element;

    const handleTouchStart = (e) => {
      // Prevent default to avoid scrolling
      e.preventDefault();
      
      const touch = e.touches[0];
      const rect = element.getBoundingClientRect();
      
      dragOffset.current = {
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top
      };

      initialPosition.current = {
        x: rect.left,
        y: rect.top
      };

      setIsDragging(true);
      
      if (onDragStart) {
        onDragStart({ x: rect.left, y: rect.top });
      }

      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);
    };

    const handleTouchMove = (e) => {
      if (!isDragging && !dragOffset.current) return;

      e.preventDefault();

      const touch = e.touches[0];
      let newX = touch.clientX - dragOffset.current.x;
      let newY = touch.clientY - dragOffset.current.y;

      // Apply same bounds logic as mouse events
      if (bounds) {
        const elementRect = element.getBoundingClientRect();
        const elementWidth = elementRect.width;
        const elementHeight = elementRect.height;

        if (bounds.left !== undefined) {
          newX = Math.max(bounds.left, newX);
        }
        if (bounds.right !== undefined) {
          newX = Math.min(bounds.right - elementWidth, newX);
        }
        if (bounds.top !== undefined) {
          newY = Math.max(bounds.top, newY);
        }
        if (bounds.bottom !== undefined) {
          newY = Math.min(bounds.bottom - elementHeight, newY);
        }
      } else {
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const elementRect = element.getBoundingClientRect();
        const minVisibleSize = 100;

        newX = Math.max(-elementRect.width + minVisibleSize, newX);
        newX = Math.min(viewportWidth - minVisibleSize, newX);
        newY = Math.max(0, newY);
        newY = Math.min(viewportHeight - minVisibleSize, newY);
      }

      if (onDrag) {
        onDrag({ x: newX, y: newY });
      }
    };

    const handleTouchEnd = (e) => {
      e.preventDefault();
      
      setIsDragging(false);
      dragOffset.current = { x: 0, y: 0 };

      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);

      if (onDragEnd) {
        const rect = element.getBoundingClientRect();
        onDragEnd({ x: rect.left, y: rect.top });
      }
    };

    handle.addEventListener('touchstart', handleTouchStart, { passive: false });

    return () => {
      handle.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [disabled, elementRef, handleRef, onDragStart, onDrag, onDragEnd, bounds, isDragging]);

  return {
    isDragging
  };
};
