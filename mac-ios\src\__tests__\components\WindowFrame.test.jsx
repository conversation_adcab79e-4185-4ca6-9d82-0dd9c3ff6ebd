import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from 'styled-components';
import WindowFrame from '../../components/window/WindowFrame';
import { useThemeStore } from '../../store';

// Mock the stores
vi.mock('../../store', () => ({
  useWindowStore: () => ({
    getWindowById: vi.fn(() => ({
      id: 'test-window',
      title: 'Test Window',
      content: 'Test content',
      minimized: false,
      maximized: false,
      position: { x: 100, y: 100 },
      size: { width: 600, height: 400 },
      zIndex: 1,
      appId: 'test-app'
    })),
    closeWindow: vi.fn(),
    minimizeWindow: vi.fn(),
    maximizeWindow: vi.fn(),
    bringToFront: vi.fn(),
    updateWindowPosition: vi.fn(),
    activeWindow: 'test-window',
    setDragging: vi.fn()
  }),
  useThemeStore: () => ({
    getCurrentTheme: () => ({
      colors: {
        primary: '#007AFF',
        surface: '#F2F2F7',
        background: '#FFFFFF',
        text: '#000000',
        textSecondary: '#6D6D70',
        border: '#C6C6C8'
      },
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem'
      },
      borderRadius: {
        sm: '0.375rem',
        md: '0.5rem',
        lg: '0.75rem'
      },
      shadows: {
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
      }
    })
  })
}));

// Mock the useDraggable hook
vi.mock('../../hooks/useDraggable', () => ({
  useDraggable: () => ({
    isDragging: false
  })
}));

const TestWrapper = ({ children }) => {
  const { getCurrentTheme } = useThemeStore();
  const theme = getCurrentTheme();
  
  return (
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  );
};

describe('WindowFrame Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders window frame with title', () => {
    render(
      <TestWrapper>
        <WindowFrame windowId="test-window">
          <div>Test Content</div>
        </WindowFrame>
      </TestWrapper>
    );

    expect(screen.getByText('Test Window')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders window controls', () => {
    render(
      <TestWrapper>
        <WindowFrame windowId="test-window">
          <div>Test Content</div>
        </WindowFrame>
      </TestWrapper>
    );

    expect(screen.getByLabelText('Close window')).toBeInTheDocument();
    expect(screen.getByLabelText('Minimize window')).toBeInTheDocument();
    expect(screen.getByLabelText('Maximize window')).toBeInTheDocument();
  });

  it('handles close button click', () => {
    const { useWindowStore } = require('../../store');
    const mockCloseWindow = vi.fn();
    useWindowStore.mockReturnValue({
      ...useWindowStore(),
      closeWindow: mockCloseWindow
    });

    render(
      <TestWrapper>
        <WindowFrame windowId="test-window">
          <div>Test Content</div>
        </WindowFrame>
      </TestWrapper>
    );

    fireEvent.click(screen.getByLabelText('Close window'));
    expect(mockCloseWindow).toHaveBeenCalledWith('test-window');
  });

  it('handles minimize button click', () => {
    const { useWindowStore } = require('../../store');
    const mockMinimizeWindow = vi.fn();
    useWindowStore.mockReturnValue({
      ...useWindowStore(),
      minimizeWindow: mockMinimizeWindow
    });

    render(
      <TestWrapper>
        <WindowFrame windowId="test-window">
          <div>Test Content</div>
        </WindowFrame>
      </TestWrapper>
    );

    fireEvent.click(screen.getByLabelText('Minimize window'));
    expect(mockMinimizeWindow).toHaveBeenCalledWith('test-window');
  });

  it('handles maximize button click', () => {
    const { useWindowStore } = require('../../store');
    const mockMaximizeWindow = vi.fn();
    useWindowStore.mockReturnValue({
      ...useWindowStore(),
      maximizeWindow: mockMaximizeWindow
    });

    render(
      <TestWrapper>
        <WindowFrame windowId="test-window">
          <div>Test Content</div>
        </WindowFrame>
      </TestWrapper>
    );

    fireEvent.click(screen.getByLabelText('Maximize window'));
    expect(mockMaximizeWindow).toHaveBeenCalledWith('test-window');
  });

  it('brings window to front on click', () => {
    const { useWindowStore } = require('../../store');
    const mockBringToFront = vi.fn();
    useWindowStore.mockReturnValue({
      ...useWindowStore(),
      bringToFront: mockBringToFront,
      activeWindow: 'other-window' // Not active initially
    });

    render(
      <TestWrapper>
        <WindowFrame windowId="test-window">
          <div>Test Content</div>
        </WindowFrame>
      </TestWrapper>
    );

    fireEvent.click(screen.getByText('Test Content').closest('[data-testid="window-container"]') || screen.getByText('Test Content'));
    expect(mockBringToFront).toHaveBeenCalledWith('test-window');
  });

  it('does not render when window is minimized', () => {
    const { useWindowStore } = require('../../store');
    useWindowStore.mockReturnValue({
      ...useWindowStore(),
      getWindowById: vi.fn(() => ({
        id: 'test-window',
        title: 'Test Window',
        minimized: true,
        position: { x: 100, y: 100 },
        size: { width: 600, height: 400 },
        zIndex: 1
      }))
    });

    const { container } = render(
      <TestWrapper>
        <WindowFrame windowId="test-window">
          <div>Test Content</div>
        </WindowFrame>
      </TestWrapper>
    );

    expect(container.firstChild).toBeNull();
  });

  it('does not render when window does not exist', () => {
    const { useWindowStore } = require('../../store');
    useWindowStore.mockReturnValue({
      ...useWindowStore(),
      getWindowById: vi.fn(() => null)
    });

    const { container } = render(
      <TestWrapper>
        <WindowFrame windowId="nonexistent-window">
          <div>Test Content</div>
        </WindowFrame>
      </TestWrapper>
    );

    expect(container.firstChild).toBeNull();
  });
});
