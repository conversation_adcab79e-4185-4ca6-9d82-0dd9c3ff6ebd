import React, { useState, useCallback, useEffect } from 'react';
import styled from 'styled-components';
import { 
  RiDeleteBack2Line,
  RiSpaceLine,
  RiArrowUpSLine,
  RiArrowDownSLine,
  RiKeyboardLine,
  RiCloseLine
} from 'react-icons/ri';
import { glassEffect } from '../../styles/components';

/**
 * Styled components for the virtual keyboard
 */
const KeyboardContainer = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  ${glassEffect}
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: ${props => props.theme.spacing.md};
  z-index: 2000;
  transform: translateY(${props => props.visible ? '0' : '100%'});
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  @media (min-width: 768px) {
    display: none; /* Hide on desktop by default */
  }
`;

const KeyboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.sm};
  padding: 0 ${props => props.theme.spacing.sm};
`;

const KeyboardTitle = styled.span`
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs};
  border-radius: ${props => props.theme.borderRadius.sm};
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const KeyboardGrid = styled.div`
  display: grid;
  gap: ${props => props.theme.spacing.xs};
  grid-template-columns: repeat(10, 1fr);
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const KeyboardRow = styled.div`
  display: grid;
  gap: ${props => props.theme.spacing.xs};
  margin-bottom: ${props => props.theme.spacing.xs};
  
  &.row-1 {
    grid-template-columns: repeat(10, 1fr);
  }
  
  &.row-2 {
    grid-template-columns: repeat(9, 1fr);
  }
  
  &.row-3 {
    grid-template-columns: 1.5fr repeat(7, 1fr) 1.5fr;
  }
  
  &.row-4 {
    grid-template-columns: 1fr 1fr 4fr 1fr 1fr;
  }
`;

const Key = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: ${props => props.theme.borderRadius.sm};
  color: white;
  font-size: 1rem;
  font-weight: 500;
  padding: ${props => props.theme.spacing.sm};
  min-height: 2.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  &:active {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(0);
  }

  ${props => props.special && `
    background: rgba(255, 255, 255, 0.15);
    font-size: 0.875rem;
  `}

  ${props => props.wide && `
    grid-column: span 2;
  `}

  ${props => props.space && `
    grid-column: span 4;
  `}

  ${props => props.shift && props.active && `
    background: ${props.theme.colors.primary}66;
    border-color: ${props.theme.colors.primary};
  `}
`;

const PredictiveText = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.sm};
  padding: 0 ${props => props.theme.spacing.sm};
  overflow-x: auto;
`;

const Suggestion = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: ${props => props.theme.borderRadius.md};
  color: white;
  font-size: 0.875rem;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

/**
 * Keyboard layouts
 */
const qwertyLayout = [
  ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
  ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
  ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
  ['shift', 'z', 'x', 'c', 'v', 'b', 'n', 'm', 'backspace']
];

const shiftLayout = [
  ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')'],
  ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
  ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
  ['shift', 'Z', 'X', 'C', 'V', 'B', 'N', 'M', 'backspace']
];

/**
 * Virtual Keyboard component for touch devices
 * Provides on-screen keyboard with predictive text
 */
const VirtualKeyboard = ({ visible, onClose, onKeyPress, targetInput }) => {
  const [isShiftActive, setIsShiftActive] = useState(false);
  const [currentText, setCurrentText] = useState('');
  const [suggestions, setSuggestions] = useState([]);

  const layout = isShiftActive ? shiftLayout : qwertyLayout;

  // Simple word suggestions (in a real app, this would use a proper dictionary/API)
  const generateSuggestions = useCallback((text) => {
    const words = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'man', 'end', 'few', 'got', 'let', 'put', 'say', 'she', 'too', 'use'];
    
    if (!text || text.length < 2) return [];
    
    return words
      .filter(word => word.startsWith(text.toLowerCase()))
      .slice(0, 3);
  }, []);

  // Handle key press
  const handleKeyPress = useCallback((key) => {
    let processedKey = key;
    
    switch (key) {
      case 'shift':
        setIsShiftActive(!isShiftActive);
        return;
      
      case 'backspace':
        setCurrentText(prev => {
          const newText = prev.slice(0, -1);
          setSuggestions(generateSuggestions(newText.split(' ').pop()));
          return newText;
        });
        processedKey = 'Backspace';
        break;
      
      case 'space':
        setCurrentText(prev => prev + ' ');
        setSuggestions([]);
        processedKey = ' ';
        break;
      
      case 'enter':
        processedKey = 'Enter';
        break;
      
      default:
        setCurrentText(prev => {
          const newText = prev + key;
          const currentWord = newText.split(' ').pop();
          setSuggestions(generateSuggestions(currentWord));
          return newText;
        });
        
        // Auto-disable shift after typing a character (except for special keys)
        if (isShiftActive && key.length === 1) {
          setIsShiftActive(false);
        }
        break;
    }
    
    // Call the onKeyPress callback if provided
    if (onKeyPress) {
      onKeyPress(processedKey);
    }
    
    // Simulate keyboard event for the target input
    if (targetInput && targetInput.current) {
      const event = new KeyboardEvent('keydown', {
        key: processedKey,
        code: `Key${key.toUpperCase()}`,
        bubbles: true
      });
      targetInput.current.dispatchEvent(event);
    }
  }, [isShiftActive, onKeyPress, targetInput, generateSuggestions]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((suggestion) => {
    const words = currentText.split(' ');
    words[words.length - 1] = suggestion;
    const newText = words.join(' ') + ' ';
    
    setCurrentText(newText);
    setSuggestions([]);
    
    if (onKeyPress) {
      // Send the suggestion as a series of key presses
      suggestion.split('').forEach(char => onKeyPress(char));
      onKeyPress(' ');
    }
  }, [currentText, onKeyPress]);

  // Render individual key
  const renderKey = useCallback((key, index) => {
    const isSpecialKey = ['shift', 'backspace', 'space', 'enter'].includes(key);
    
    let keyContent = key;
    let keyProps = {};
    
    switch (key) {
      case 'shift':
        keyContent = <RiArrowUpSLine />;
        keyProps = { special: true, shift: true, active: isShiftActive };
        break;
      case 'backspace':
        keyContent = <RiDeleteBack2Line />;
        keyProps = { special: true };
        break;
      case 'space':
        keyContent = <RiSpaceLine />;
        keyProps = { special: true, space: true };
        break;
      case 'enter':
        keyContent = '↵';
        keyProps = { special: true };
        break;
      default:
        keyContent = key;
        break;
    }
    
    return (
      <Key
        key={`${key}-${index}`}
        onClick={() => handleKeyPress(key)}
        {...keyProps}
      >
        {keyContent}
      </Key>
    );
  }, [isShiftActive, handleKeyPress]);

  // Handle escape key to close keyboard
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && visible) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [visible, onClose]);

  if (!visible) return null;

  return (
    <KeyboardContainer visible={visible}>
      <KeyboardHeader>
        <KeyboardTitle>Virtual Keyboard</KeyboardTitle>
        <CloseButton onClick={onClose}>
          <RiCloseLine size={20} />
        </CloseButton>
      </KeyboardHeader>

      {suggestions.length > 0 && (
        <PredictiveText>
          {suggestions.map((suggestion, index) => (
            <Suggestion
              key={index}
              onClick={() => handleSuggestionSelect(suggestion)}
            >
              {suggestion}
            </Suggestion>
          ))}
        </PredictiveText>
      )}

      {layout.map((row, rowIndex) => (
        <KeyboardRow key={rowIndex} className={`row-${rowIndex + 1}`}>
          {row.map((key, keyIndex) => renderKey(key, keyIndex))}
        </KeyboardRow>
      ))}

      <KeyboardRow className="row-4">
        <Key special onClick={() => handleKeyPress('123')}>123</Key>
        <Key special onClick={() => handleKeyPress('emoji')}>😀</Key>
        <Key space onClick={() => handleKeyPress('space')}>
          <RiSpaceLine />
        </Key>
        <Key special onClick={() => handleKeyPress('.')}>.</Key>
        <Key special onClick={() => handleKeyPress('enter')}>↵</Key>
      </KeyboardRow>
    </KeyboardContainer>
  );
};

export default VirtualKeyboard;
