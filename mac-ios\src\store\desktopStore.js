import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import {
  RiFolderLine,
  RiGlobalLine,
  RiMessage3Line,
  RiMailLine,
  RiCameraLine,
  RiHeadphoneLine,
  RiStore2Line,
  RiStickyNoteLine,
  RiCalendarLine,
  RiSettings3Line,
} from 'react-icons/ri';

/**
 * Desktop icon interface
 */
export interface DesktopIcon {
  id: string;
  name: string;
  icon: React.ComponentType;
  color: string;
  position: { x: number; y: number };
  selected: boolean;
}

/**
 * Desktop store interface
 */
interface DesktopStore {
  icons: DesktopIcon[];
  selectedIcons: string[];
  wallpaper: string;
  gridSize: { cols: number; rows: number };
  
  // Actions
  selectIcon: (iconId: string, multiSelect?: boolean) => void;
  deselectAll: () => void;
  moveIcon: (iconId: string, position: { x: number; y: number }) => void;
  setWallpaper: (wallpaperUrl: string) => void;
  updateGridSize: (cols: number, rows: number) => void;
  getIconById: (iconId: string) => DesktopIcon | undefined;
}

const defaultIcons: DesktopIcon[] = [
  { 
    id: 'finder', 
    name: 'Finder', 
    icon: RiFolderLine, 
    color: 'from-blue-400 to-blue-600',
    position: { x: 0, y: 0 },
    selected: false
  },
  { 
    id: 'safari', 
    name: 'Safari', 
    icon: RiGlobalLine, 
    color: 'from-blue-500 to-blue-700',
    position: { x: 1, y: 0 },
    selected: false
  },
  { 
    id: 'messages', 
    name: 'Messages', 
    icon: RiMessage3Line, 
    color: 'from-green-400 to-green-600',
    position: { x: 2, y: 0 },
    selected: false
  },
  { 
    id: 'mail', 
    name: 'Mail', 
    icon: RiMailLine, 
    color: 'from-blue-400 to-indigo-600',
    position: { x: 3, y: 0 },
    selected: false
  },
  { 
    id: 'photos', 
    name: 'Photos', 
    icon: RiCameraLine, 
    color: 'from-purple-400 to-purple-600',
    position: { x: 0, y: 1 },
    selected: false
  },
  { 
    id: 'music', 
    name: 'Music', 
    icon: RiHeadphoneLine, 
    color: 'from-pink-400 to-pink-600',
    position: { x: 1, y: 1 },
    selected: false
  },
  { 
    id: 'app-store', 
    name: 'App Store', 
    icon: RiStore2Line, 
    color: 'from-blue-400 to-blue-600',
    position: { x: 2, y: 1 },
    selected: false
  },
  { 
    id: 'notes', 
    name: 'Notes', 
    icon: RiStickyNoteLine, 
    color: 'from-yellow-400 to-yellow-600',
    position: { x: 3, y: 1 },
    selected: false
  },
  { 
    id: 'calendar', 
    name: 'Calendar', 
    icon: RiCalendarLine, 
    color: 'from-red-400 to-red-600',
    position: { x: 0, y: 2 },
    selected: false
  },
  { 
    id: 'settings', 
    name: 'Settings', 
    icon: RiSettings3Line, 
    color: 'from-gray-400 to-gray-600',
    position: { x: 1, y: 2 },
    selected: false
  },
];

const defaultWallpaper = "https://readdy.ai/api/search-image?query=Stunning%20macOS%20desktop%20wallpaper%20with%20soft%20gradient%20colors%2C%20mountains%20in%20the%20distance%2C%20beautiful%20sunset%20sky%20with%20pink%20and%20purple%20hues%2C%20ultra%20high%20definition%2C%20professional%20photography%2C%20serene%20landscape&width=1920&height=1080&seq=1&orientation=landscape";

const useDesktopStore = create<DesktopStore>()(
  subscribeWithSelector((set, get) => ({
    icons: defaultIcons,
    selectedIcons: [],
    wallpaper: defaultWallpaper,
    gridSize: { cols: 10, rows: 8 },

    selectIcon: (iconId: string, multiSelect = false) => {
      set(state => {
        if (multiSelect) {
          const isSelected = state.selectedIcons.includes(iconId);
          return {
            selectedIcons: isSelected
              ? state.selectedIcons.filter(id => id !== iconId)
              : [...state.selectedIcons, iconId],
            icons: state.icons.map(icon => ({
              ...icon,
              selected: icon.id === iconId ? !icon.selected : icon.selected
            }))
          };
        } else {
          return {
            selectedIcons: [iconId],
            icons: state.icons.map(icon => ({
              ...icon,
              selected: icon.id === iconId
            }))
          };
        }
      });
    },

    deselectAll: () => {
      set(state => ({
        selectedIcons: [],
        icons: state.icons.map(icon => ({ ...icon, selected: false }))
      }));
    },

    moveIcon: (iconId: string, position: { x: number; y: number }) => {
      set(state => ({
        icons: state.icons.map(icon =>
          icon.id === iconId ? { ...icon, position } : icon
        )
      }));
    },

    setWallpaper: (wallpaperUrl: string) => {
      set({ wallpaper: wallpaperUrl });
    },

    updateGridSize: (cols: number, rows: number) => {
      set({ gridSize: { cols, rows } });
    },

    getIconById: (iconId: string) => {
      return get().icons.find(icon => icon.id === iconId);
    }
  }))
);

export default useDesktopStore;
