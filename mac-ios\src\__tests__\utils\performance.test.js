import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  debounce, 
  throttle, 
  PerformanceMonitor, 
  VirtualScroller,
  memoryUtils,
  imageUtils
} from '../../utils/performance';

describe('Performance Utilities', () => {
  beforeEach(() => {
    vi.clearAllTimers();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('debounce', () => {
    it('should delay function execution', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      expect(mockFn).not.toHaveBeenCalled();

      vi.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should cancel previous calls', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      vi.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should execute immediately when immediate is true', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100, true);

      debouncedFn();
      expect(mockFn).toHaveBeenCalledTimes(1);

      debouncedFn();
      expect(mockFn).toHaveBeenCalledTimes(1); // Should not call again
    });
  });

  describe('throttle', () => {
    it('should limit function execution frequency', () => {
      const mockFn = vi.fn();
      const throttledFn = throttle(mockFn, 100);

      throttledFn();
      throttledFn();
      throttledFn();

      expect(mockFn).toHaveBeenCalledTimes(1);

      vi.advanceTimersByTime(100);
      throttledFn();
      expect(mockFn).toHaveBeenCalledTimes(2);
    });
  });

  describe('PerformanceMonitor', () => {
    let monitor;

    beforeEach(() => {
      monitor = new PerformanceMonitor();
      // Mock performance.now
      vi.spyOn(performance, 'now').mockReturnValue(1000);
    });

    it('should start and end timing correctly', () => {
      monitor.startTiming('test');
      
      performance.now.mockReturnValue(1100);
      const duration = monitor.endTiming('test');
      
      expect(duration).toBe(100);
      expect(monitor.getMetric('test')).toBe(100);
    });

    it('should warn for slow operations', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      monitor.startTiming('slow-operation');
      performance.now.mockReturnValue(1200); // 200ms duration
      monitor.endTiming('slow-operation');
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Performance warning: "slow-operation" took 200.00ms')
      );
      
      consoleSpy.mockRestore();
    });

    it('should handle missing metrics gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      const duration = monitor.endTiming('nonexistent');
      expect(duration).toBe(0);
      expect(consoleSpy).toHaveBeenCalledWith(
        'Performance metric "nonexistent" not found'
      );
      
      consoleSpy.mockRestore();
    });

    it('should clear metrics', () => {
      monitor.startTiming('test');
      monitor.endTiming('test');
      
      expect(monitor.getMetric('test')).toBe(0);
      
      monitor.clearMetrics();
      expect(monitor.getMetric('test')).toBeNull();
    });
  });

  describe('VirtualScroller', () => {
    let scroller;

    beforeEach(() => {
      scroller = new VirtualScroller({
        itemHeight: 50,
        containerHeight: 400,
        overscan: 5
      });
    });

    it('should calculate visible range correctly', () => {
      const range = scroller.getVisibleRange(100, 0);
      
      expect(range.startIndex).toBe(0);
      expect(range.endIndex).toBe(12); // Math.max(0, 0 - 5) to Math.min(99, 8 + 5)
      expect(range.offsetY).toBe(0);
      expect(range.totalHeight).toBe(5000); // 100 items * 50px
    });

    it('should handle scrolling', () => {
      const range = scroller.getVisibleRange(100, 250); // Scrolled down
      
      expect(range.startIndex).toBe(0); // Math.max(0, 5 - 5)
      expect(range.endIndex).toBe(18); // Math.min(99, 13 + 5)
      expect(range.offsetY).toBe(0);
    });

    it('should respect overscan', () => {
      const range = scroller.getVisibleRange(100, 500);
      
      // Should include overscan items before and after visible area
      expect(range.startIndex).toBe(5); // Math.max(0, 10 - 5)
      expect(range.endIndex).toBe(23); // Math.min(99, 18 + 5)
    });
  });

  describe('memoryUtils', () => {
    it('should get memory info when available', () => {
      // Mock performance.memory
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 1000000,
          totalJSHeapSize: 2000000,
          jsHeapSizeLimit: 4000000
        },
        configurable: true
      });

      const memoryInfo = memoryUtils.getMemoryInfo();
      
      expect(memoryInfo).toEqual({
        usedJSHeapSize: 1000000,
        totalJSHeapSize: 2000000,
        jsHeapSizeLimit: 4000000,
        usedPercentage: 25
      });
    });

    it('should return null when memory info is not available', () => {
      // Remove performance.memory
      delete performance.memory;
      
      const memoryInfo = memoryUtils.getMemoryInfo();
      expect(memoryInfo).toBeNull();
    });

    it('should clean up event listeners', () => {
      const mockElement = {
        cloneNode: vi.fn().mockReturnValue({ cloned: true }),
        parentNode: {
          replaceChild: vi.fn()
        }
      };

      const result = memoryUtils.cleanupEventListeners(mockElement);
      
      expect(mockElement.cloneNode).toHaveBeenCalledWith(true);
      expect(mockElement.parentNode.replaceChild).toHaveBeenCalled();
      expect(result).toEqual({ cloned: true });
    });
  });

  describe('imageUtils', () => {
    it('should preload images', async () => {
      // Mock Image constructor
      const mockImages = [];
      global.Image = class {
        constructor() {
          mockImages.push(this);
        }
        set src(value) {
          this._src = value;
          // Simulate successful load
          setTimeout(() => this.onload && this.onload(), 0);
        }
        get src() {
          return this._src;
        }
      };

      const urls = ['image1.jpg', 'image2.jpg'];
      const promise = imageUtils.preloadImages(urls);
      
      // Advance timers to trigger onload
      vi.advanceTimersByTime(0);
      
      await expect(promise).resolves.toBeUndefined();
      expect(mockImages).toHaveLength(2);
      expect(mockImages[0].src).toBe('image1.jpg');
      expect(mockImages[1].src).toBe('image2.jpg');
    });

    it('should handle image load errors', async () => {
      global.Image = class {
        set src(value) {
          this._src = value;
          // Simulate error
          setTimeout(() => this.onerror && this.onerror(), 0);
        }
        get src() {
          return this._src;
        }
      };

      const urls = ['invalid-image.jpg'];
      const promise = imageUtils.preloadImages(urls);
      
      vi.advanceTimersByTime(0);
      
      await expect(promise).rejects.toBeUndefined();
    });
  });
});
