import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  RiPaletteLine,
  RiImageLine,
  RiComputerLine,
  RiSunLine,
  RiMoonLine,
  RiContrastLine
} from 'react-icons/ri';
import { Button, Flex, Text, Card } from '../../../styles/components';
import { useThemeStore, useDesktopStore } from '../../../store';

/**
 * Styled components for the Settings window
 */
const SettingsContainer = styled.div`
  height: 100%;
  display: flex;
`;

const SettingsSidebar = styled.div`
  width: 200px;
  background: ${props => props.theme.colors.surface};
  border-right: 1px solid ${props => props.theme.colors.border};
  padding: ${props => props.theme.spacing.md};
`;

const SidebarItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-bottom: ${props => props.theme.spacing.xs};
  
  &:hover {
    background: ${props => props.theme.colors.background};
  }

  ${props => props.active && `
    background: ${props.theme.colors.primary};
    color: white;
  `}
`;

const SettingsContent = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.xl};
  overflow: auto;
`;

const SettingsSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const SettingGroup = styled(Card)`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const SettingItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.spacing.md} 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};

  &:last-child {
    border-bottom: none;
  }
`;

const SettingLabel = styled.div`
  flex: 1;
`;

const SettingTitle = styled(Text)`
  font-weight: 500;
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const SettingDescription = styled(Text)`
  font-size: 0.875rem;
  color: ${props => props.theme.colors.textSecondary};
`;

const ThemeOption = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.md};
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;

  &:hover {
    background: ${props => props.theme.colors.surface};
  }

  ${props => props.selected && `
    border-color: ${props.theme.colors.primary};
    background: ${props.theme.colors.primary}11;
  `}
`;

const ThemePreview = styled.div`
  width: 2rem;
  height: 2rem;
  border-radius: ${props => props.theme.borderRadius.sm};
  border: 1px solid ${props => props.theme.colors.border};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
`;

const WallpaperGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: ${props => props.theme.spacing.md};
  margin-top: ${props => props.theme.spacing.md};
`;

const WallpaperOption = styled.div`
  aspect-ratio: 16/10;
  border-radius: ${props => props.theme.borderRadius.md};
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 3px solid transparent;
  overflow: hidden;

  &:hover {
    transform: scale(1.05);
  }

  ${props => props.selected && `
    border-color: ${props.theme.colors.primary};
  `}

  ${props => props.gradient && `
    background: ${props.gradient};
  `}
`;

/**
 * Settings categories
 */
const settingsCategories = [
  { id: 'appearance', name: 'Appearance', icon: RiPaletteLine },
  { id: 'desktop', name: 'Desktop & Dock', icon: RiComputerLine },
  { id: 'wallpaper', name: 'Wallpaper', icon: RiImageLine },
];

/**
 * Wallpaper options
 */
const wallpaperOptions = [
  { id: 'monterey', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
  { id: 'sunset', gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)' },
  { id: 'ocean', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
  { id: 'forest', gradient: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)' },
  { id: 'space', gradient: 'linear-gradient(135deg, #2c3e50 0%, #000000 100%)' },
  { id: 'aurora', gradient: 'linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)' },
];

/**
 * Settings window content component
 * Provides theme switching and system preferences
 * 
 * @param {Object} props
 * @param {Object} props.window - Window state object
 */
const SettingsWindow = ({ window }) => {
  const [activeCategory, setActiveCategory] = useState('appearance');

  const { theme, setThemeMode, toggleTheme } = useThemeStore();
  const { wallpaper, setWallpaper } = useDesktopStore();

  // Get current wallpaper ID from URL
  const getCurrentWallpaperId = () => {
    const wallpaperMap = {
      'linear-gradient(135deg, #667eea 0%, #764ba2 100%)': 'monterey',
      'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)': 'sunset',
      'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)': 'forest',
      'linear-gradient(135deg, #2c3e50 0%, #000000 100%)': 'space',
      'linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)': 'aurora'
    };

    // Find matching wallpaper or default to monterey
    for (const [gradient, id] of Object.entries(wallpaperMap)) {
      if (wallpaper.includes(gradient)) return id;
    }
    return 'monterey';
  };

  const [selectedWallpaper, setSelectedWallpaper] = useState(getCurrentWallpaperId());

  const renderAppearanceSettings = () => (
    <SettingsSection>
      <SectionTitle>Appearance</SectionTitle>
      
      <SettingGroup>
        <SettingItem>
          <SettingLabel>
            <SettingTitle>Theme</SettingTitle>
            <SettingDescription>
              Choose how the interface looks
            </SettingDescription>
          </SettingLabel>
        </SettingItem>
        
        <Flex direction="column" gap="sm">
          <ThemeOption 
            selected={theme.mode === 'light'}
            onClick={() => setThemeMode('light')}
          >
            <ThemePreview style={{ background: '#ffffff', color: '#000000' }}>
              <RiSunLine />
            </ThemePreview>
            <Text>Light</Text>
          </ThemeOption>
          
          <ThemeOption 
            selected={theme.mode === 'dark'}
            onClick={() => setThemeMode('dark')}
          >
            <ThemePreview style={{ background: '#000000', color: '#ffffff' }}>
              <RiMoonLine />
            </ThemePreview>
            <Text>Dark</Text>
          </ThemeOption>
          
          <ThemeOption 
            selected={theme.mode === 'auto'}
            onClick={() => setThemeMode('auto')}
          >
            <ThemePreview style={{ background: 'linear-gradient(90deg, #ffffff 50%, #000000 50%)', color: '#666666' }}>
              <RiContrastLine />
            </ThemePreview>
            <Text>Auto</Text>
          </ThemeOption>
        </Flex>
      </SettingGroup>

      <SettingGroup>
        <SettingItem>
          <SettingLabel>
            <SettingTitle>Quick Toggle</SettingTitle>
            <SettingDescription>
              Switch between light and dark themes
            </SettingDescription>
          </SettingLabel>
          <Button variant="primary" onClick={toggleTheme}>
            Toggle Theme
          </Button>
        </SettingItem>
      </SettingGroup>
    </SettingsSection>
  );

  const renderWallpaperSettings = () => (
    <SettingsSection>
      <SectionTitle>Wallpaper</SectionTitle>
      
      <SettingGroup>
        <SettingItem>
          <SettingLabel>
            <SettingTitle>Desktop Wallpaper</SettingTitle>
            <SettingDescription>
              Choose a wallpaper for your desktop
            </SettingDescription>
          </SettingLabel>
        </SettingItem>
        
        <WallpaperGrid>
          {wallpaperOptions.map(option => (
            <WallpaperOption
              key={option.id}
              gradient={option.gradient}
              selected={selectedWallpaper === option.id}
              onClick={() => {
                setSelectedWallpaper(option.id);
                // Create a data URL for the gradient
                const canvas = document.createElement('canvas');
                canvas.width = 1920;
                canvas.height = 1080;
                const ctx = canvas.getContext('2d');

                // Create gradient
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);

                // Parse gradient colors (simplified)
                if (option.id === 'monterey') {
                  gradient.addColorStop(0, '#667eea');
                  gradient.addColorStop(1, '#764ba2');
                } else if (option.id === 'sunset') {
                  gradient.addColorStop(0, '#ff9a9e');
                  gradient.addColorStop(0.5, '#fecfef');
                  gradient.addColorStop(1, '#fecfef');
                } else if (option.id === 'forest') {
                  gradient.addColorStop(0, '#11998e');
                  gradient.addColorStop(1, '#38ef7d');
                } else if (option.id === 'space') {
                  gradient.addColorStop(0, '#2c3e50');
                  gradient.addColorStop(1, '#000000');
                } else if (option.id === 'aurora') {
                  gradient.addColorStop(0, '#00c6ff');
                  gradient.addColorStop(1, '#0072ff');
                }

                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Convert to data URL and set as wallpaper
                const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
                setWallpaper(dataUrl);
              }}
            />
          ))}
        </WallpaperGrid>
      </SettingGroup>
    </SettingsSection>
  );

  const renderDesktopSettings = () => (
    <SettingsSection>
      <SectionTitle>Desktop & Dock</SectionTitle>
      
      <SettingGroup>
        <SettingItem>
          <SettingLabel>
            <SettingTitle>Dock Position</SettingTitle>
            <SettingDescription>
              Choose where the dock appears on screen
            </SettingDescription>
          </SettingLabel>
          <Button variant="secondary">Bottom</Button>
        </SettingItem>
        
        <SettingItem>
          <SettingLabel>
            <SettingTitle>Dock Size</SettingTitle>
            <SettingDescription>
              Adjust the size of dock icons
            </SettingDescription>
          </SettingLabel>
          <Button variant="secondary">Medium</Button>
        </SettingItem>
        
        <SettingItem>
          <SettingLabel>
            <SettingTitle>Magnification</SettingTitle>
            <SettingDescription>
              Enable icon magnification on hover
            </SettingDescription>
          </SettingLabel>
          <Button variant="secondary">On</Button>
        </SettingItem>
      </SettingGroup>
    </SettingsSection>
  );

  const renderContent = () => {
    switch (activeCategory) {
      case 'appearance':
        return renderAppearanceSettings();
      case 'wallpaper':
        return renderWallpaperSettings();
      case 'desktop':
        return renderDesktopSettings();
      default:
        return renderAppearanceSettings();
    }
  };

  return (
    <SettingsContainer>
      <SettingsSidebar>
        {settingsCategories.map(category => {
          const IconComponent = category.icon;
          return (
            <SidebarItem
              key={category.id}
              active={activeCategory === category.id}
              onClick={() => setActiveCategory(category.id)}
            >
              <IconComponent size={16} />
              <Text size="sm">{category.name}</Text>
            </SidebarItem>
          );
        })}
      </SettingsSidebar>

      <SettingsContent>
        {renderContent()}
      </SettingsContent>
    </SettingsContainer>
  );
};

export default SettingsWindow;
