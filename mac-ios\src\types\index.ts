/**
 * Global type definitions for the macOS/iOS operating system
 */

import { ComponentType } from 'react';

/**
 * Base types
 */
export type UUID = string;
export type Timestamp = string;
export type Color = string;
export type IconComponent = ComponentType<{ size?: number; className?: string }>;

/**
 * Position and size types
 */
export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Bounds {
  left?: number;
  right?: number;
  top?: number;
  bottom?: number;
}

/**
 * Window system types
 */
export interface WindowState {
  id: UUID;
  title: string;
  content: string;
  minimized: boolean;
  maximized: boolean;
  position: Position;
  size: Size;
  zIndex: number;
  appId: string;
  prevPosition?: Position;
  prevSize?: Size;
}

export interface WindowManagerState {
  windows: WindowState[];
  activeWindow: UUID | null;
  maxZIndex: number;
  isDragging: boolean;
  draggedWindow: UUID | null;
}

/**
 * Application types
 */
export type AppCategory = 'system' | 'productivity' | 'entertainment' | 'utility';

export interface AppDefinition {
  id: UUID;
  name: string;
  icon: IconComponent;
  color: Color;
  isRunning: boolean;
  category: AppCategory;
}

/**
 * Desktop types
 */
export interface DesktopIcon {
  id: UUID;
  name: string;
  icon: IconComponent;
  color: Color;
  position: Position;
  selected: boolean;
}

export interface DesktopState {
  icons: DesktopIcon[];
  selectedIcons: UUID[];
  wallpaper: string;
  gridSize: {
    cols: number;
    rows: number;
  };
}

/**
 * Theme types
 */
export type ThemeMode = 'light' | 'dark' | 'auto';

export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  accent: string;
  success: string;
  warning: string;
  error: string;
  glass: string;
  glassStrong: string;
}

export interface ThemeConfig {
  mode: ThemeMode;
  colors: ThemeColors;
  fonts: {
    system: string;
    mono: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

/**
 * File system types
 */
export type FileType = 'file' | 'folder';

export interface FileSystemItem {
  id: UUID;
  name: string;
  type: FileType;
  path: string;
  parentId: UUID | null;
  size?: string;
  modified?: Timestamp;
  created?: Timestamp;
  content?: string;
}

export interface FileSystemState {
  currentPath: string;
  files: FileSystemItem[];
  selectedFiles: UUID[];
  viewMode: 'grid' | 'list';
  searchQuery: string;
  history: string[];
  historyIndex: number;
}

/**
 * Performance types
 */
export interface PerformanceMetric {
  startTime: number;
  endTime: number | null;
  duration: number | null;
}

export interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  usedPercentage: number;
}

export interface VirtualScrollRange {
  startIndex: number;
  endIndex: number;
  offsetY: number;
  totalHeight: number;
}

/**
 * Event types
 */
export interface DragEvent {
  position: Position;
  offset: Position;
  target: HTMLElement;
}

export interface KeyboardEvent {
  key: string;
  code: string;
  ctrlKey: boolean;
  metaKey: boolean;
  shiftKey: boolean;
  altKey: boolean;
}

export interface MouseEvent {
  clientX: number;
  clientY: number;
  button: number;
  ctrlKey: boolean;
  metaKey: boolean;
  shiftKey: boolean;
  altKey: boolean;
}

/**
 * Component prop types
 */
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  'data-testid'?: string;
}

export interface WindowFrameProps extends BaseComponentProps {
  windowId: UUID;
}

export interface DockIconProps extends BaseComponentProps {
  app: AppDefinition;
  index: number;
  onClick: (app: AppDefinition) => void;
  onHover: (appId: UUID | null) => void;
  calculateMagnification: (iconRect: DOMRect) => number;
  mousePosition: Position;
}

export interface DesktopIconProps extends BaseComponentProps {
  icon: DesktopIcon;
  onOpen: (iconId: UUID) => void;
  onSelect: (iconId: UUID, multiSelect?: boolean) => void;
}

/**
 * Store action types
 */
export interface WindowActions {
  openWindow: (appId: string, title: string, content?: string) => void;
  closeWindow: (windowId: UUID) => void;
  minimizeWindow: (windowId: UUID) => void;
  maximizeWindow: (windowId: UUID) => void;
  restoreWindow: (windowId: UUID) => void;
  bringToFront: (windowId: UUID) => void;
  updateWindowPosition: (windowId: UUID, position: Position) => void;
  updateWindowSize: (windowId: UUID, size: Size) => void;
  setActiveWindow: (windowId: UUID | null) => void;
  setDragging: (isDragging: boolean, windowId?: UUID | null) => void;
  getWindowById: (windowId: UUID) => WindowState | undefined;
}

export interface DockActions {
  setAppRunning: (appId: UUID, isRunning: boolean) => void;
  setHoveredApp: (appId: UUID | null) => void;
  getAppById: (appId: UUID) => AppDefinition | undefined;
  getRunningApps: () => AppDefinition[];
}

export interface DesktopActions {
  selectIcon: (iconId: UUID, multiSelect?: boolean) => void;
  deselectAll: () => void;
  moveIcon: (iconId: UUID, position: Position) => void;
  setWallpaper: (wallpaperUrl: string) => void;
  updateGridSize: (cols: number, rows: number) => void;
  getIconById: (iconId: UUID) => DesktopIcon | undefined;
}

export interface ThemeActions {
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
  setSystemPrefersDark: (prefersDark: boolean) => void;
  getCurrentTheme: () => ThemeConfig;
}

/**
 * Utility types
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? never : K;
}[keyof T];

export type OptionalKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? K : never;
}[keyof T];

/**
 * API types
 */
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface WorkerMessage<T = any> {
  id: number;
  operation: string;
  data: T;
}

export interface WorkerResponse<T = any> {
  id: number;
  success: boolean;
  result?: T;
  error?: string;
  operation: string;
}

/**
 * Animation types
 */
export type EasingFunction = 'easeInOutQuad' | 'easeOutCubic' | 'easeInOutCubic';

export interface AnimationOptions {
  duration?: number;
  easing?: EasingFunction;
  delay?: number;
}

export interface TransitionConfig {
  property: string;
  duration: number;
  timingFunction: string;
  delay?: number;
}

/**
 * Responsive types
 */
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

export interface ResponsiveValue<T> {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
}

/**
 * Accessibility types
 */
export interface A11yProps {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  'aria-selected'?: boolean;
  'aria-checked'?: boolean;
  'aria-disabled'?: boolean;
  role?: string;
  tabIndex?: number;
}

/**
 * Form types
 */
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio';
  value: any;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: Array<{ label: string; value: any }>;
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: any) => string | null;
  };
}

export interface FormState {
  fields: Record<string, FormField>;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isValid: boolean;
}
