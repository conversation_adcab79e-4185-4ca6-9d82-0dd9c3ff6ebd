import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  RiSearchLine, 
  RiArrowLeftSLine, 
  RiArrowRightSLine,
  RiApps2Line,
  RiListUnordered,
  RiComputerLine,
  RiDownloadLine,
  RiFileLine,
  RiFileTextLine,
  RiFolderLine
} from 'react-icons/ri';
import { Input, Button, Flex, Text } from '../../../styles/components';

/**
 * Styled components for the Finder window
 */
const FinderContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const FinderToolbar = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  gap: ${props => props.theme.spacing.sm};
`;

const SearchContainer = styled.div`
  position: relative;
  flex: 1;
  max-width: 300px;
`;

const SearchInput = styled(Input)`
  padding-right: 2.5rem;
`;

const SearchIcon = styled(RiSearchLine)`
  position: absolute;
  right: ${props => props.theme.spacing.sm};
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.textSecondary};
  pointer-events: none;
`;

const FinderContent = styled.div`
  flex: 1;
  display: flex;
  height: calc(100% - 3rem);
`;

const Sidebar = styled.div`
  width: 200px;
  background: ${props => props.theme.colors.surface};
  border-right: 1px solid ${props => props.theme.colors.border};
  padding: ${props => props.theme.spacing.md};
`;

const SidebarSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const SidebarTitle = styled(Text)`
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const SidebarItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.background};
  }

  ${props => props.active && `
    background: ${props.theme.colors.primary};
    color: white;
  `}
`;

const MainContent = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
  overflow: auto;
`;

const FileGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: ${props => props.theme.spacing.md};
`;

const FileItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.surface};
  }
`;

const FileIcon = styled.div`
  width: 3rem;
  height: 3rem;
  background: ${props => props.theme.colors.primary}22;
  border-radius: ${props => props.theme.borderRadius.md};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: ${props => props.theme.spacing.sm};
  color: ${props => props.theme.colors.primary};
  font-size: 1.5rem;
`;

const FileName = styled(Text)`
  font-size: 0.75rem;
  text-align: center;
  color: ${props => props.theme.colors.text};
`;

/**
 * Mock file data
 */
const mockFiles = [
  { id: 1, name: 'Documents', type: 'folder', icon: RiFolderLine },
  { id: 2, name: 'Downloads', type: 'folder', icon: RiFolderLine },
  { id: 3, name: 'Desktop', type: 'folder', icon: RiFolderLine },
  { id: 4, name: 'README.txt', type: 'file', icon: RiFileTextLine },
  { id: 5, name: 'Project.zip', type: 'file', icon: RiFileLine },
  { id: 6, name: 'Image.png', type: 'file', icon: RiFileLine },
  { id: 7, name: 'Video.mp4', type: 'file', icon: RiFileLine },
  { id: 8, name: 'Music.mp3', type: 'file', icon: RiFileLine },
];

const sidebarItems = [
  { id: 'favorites', title: 'Favorites', items: [
    { id: 'desktop', name: 'Desktop', icon: RiComputerLine },
    { id: 'downloads', name: 'Downloads', icon: RiDownloadLine },
    { id: 'documents', name: 'Documents', icon: RiFileLine },
  ]},
];

/**
 * Finder window content component
 * Simulates macOS Finder with file browser functionality
 * 
 * @param {Object} props
 * @param {Object} props.window - Window state object
 */
const FinderWindow = ({ window }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState('grid');
  const [selectedItem, setSelectedItem] = useState('desktop');

  const filteredFiles = mockFiles.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <FinderContainer>
      <FinderToolbar>
        <Button variant="ghost" size="sm">
          <RiArrowLeftSLine />
        </Button>
        <Button variant="ghost" size="sm">
          <RiArrowRightSLine />
        </Button>
        
        <SearchContainer>
          <SearchInput
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <SearchIcon />
        </SearchContainer>

        <Button 
          variant={viewMode === 'grid' ? 'secondary' : 'ghost'} 
          size="sm"
          onClick={() => setViewMode('grid')}
        >
          <RiApps2Line />
        </Button>
        <Button 
          variant={viewMode === 'list' ? 'secondary' : 'ghost'} 
          size="sm"
          onClick={() => setViewMode('list')}
        >
          <RiListUnordered />
        </Button>
      </FinderToolbar>

      <FinderContent>
        <Sidebar>
          {sidebarItems.map(section => (
            <SidebarSection key={section.id}>
              <SidebarTitle>{section.title}</SidebarTitle>
              {section.items.map(item => {
                const IconComponent = item.icon;
                return (
                  <SidebarItem
                    key={item.id}
                    active={selectedItem === item.id}
                    onClick={() => setSelectedItem(item.id)}
                  >
                    <IconComponent size={16} />
                    <Text size="sm">{item.name}</Text>
                  </SidebarItem>
                );
              })}
            </SidebarSection>
          ))}
        </Sidebar>

        <MainContent>
          <FileGrid>
            {filteredFiles.map(file => {
              const IconComponent = file.icon;
              return (
                <FileItem key={file.id}>
                  <FileIcon>
                    <IconComponent />
                  </FileIcon>
                  <FileName>{file.name}</FileName>
                </FileItem>
              );
            })}
          </FileGrid>
        </MainContent>
      </FinderContent>
    </FinderContainer>
  );
};

export default FinderWindow;
