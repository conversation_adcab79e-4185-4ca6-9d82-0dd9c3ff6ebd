import React from 'react';
import styled from 'styled-components';
import { Text, Flex } from '../../../styles/components';

/**
 * Styled components for the default window
 */
const WindowContent = styled.div`
  padding: ${props => props.theme.spacing.lg};
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
`;

const WelcomeMessage = styled(Text)`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const Description = styled(Text)`
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.6;
`;

const FeatureList = styled.ul`
  list-style: none;
  padding: 0;
  margin: ${props => props.theme.spacing.md} 0;
`;

const FeatureItem = styled.li`
  padding: ${props => props.theme.spacing.sm} 0;
  color: ${props => props.theme.colors.textSecondary};
  
  &:before {
    content: '•';
    color: ${props => props.theme.colors.primary};
    margin-right: ${props => props.theme.spacing.sm};
  }
`;

/**
 * Default window content component
 * Used for apps that don't have specific window implementations
 * 
 * @param {Object} props
 * @param {Object} props.window - Window state object
 */
const DefaultWindow = ({ window }) => {
  return (
    <WindowContent>
      <Flex direction="column" gap="md">
        <WelcomeMessage>
          Welcome to {window.title}
        </WelcomeMessage>
        
        <Description>
          This is a default window for the {window.title} application. 
          This macOS-style operating system includes many features:
        </Description>

        <FeatureList>
          <FeatureItem>Draggable and resizable windows</FeatureItem>
          <FeatureItem>Window management with minimize/maximize</FeatureItem>
          <FeatureItem>Desktop with icon grid</FeatureItem>
          <FeatureItem>Dock with running app indicators</FeatureItem>
          <FeatureItem>Theme system with light/dark modes</FeatureItem>
          <FeatureItem>Frosted glass effects</FeatureItem>
          <FeatureItem>Touch and mobile support</FeatureItem>
        </FeatureList>

        <Description>
          Click the window controls in the top-left corner to close, minimize, or maximize this window.
          You can also drag the window around by clicking and dragging the title bar.
        </Description>
      </Flex>
    </WindowContent>
  );
};

export default DefaultWindow;
