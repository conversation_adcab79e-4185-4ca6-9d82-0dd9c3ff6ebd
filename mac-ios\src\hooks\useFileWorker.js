import { useRef, useCallback, useEffect } from 'react';

/**
 * Custom hook for using the file worker
 * Provides a clean interface for offloading file operations to a Web Worker
 */
export const useFileWorker = () => {
  const workerRef = useRef(null);
  const pendingOperations = useRef(new Map());
  const operationId = useRef(0);

  // Initialize worker
  useEffect(() => {
    try {
      workerRef.current = new Worker(
        new URL('../workers/fileWorker.js', import.meta.url),
        { type: 'module' }
      );

      workerRef.current.onmessage = (e) => {
        const { id, success, result, error, operation } = e.data;
        const pendingOperation = pendingOperations.current.get(id);

        if (pendingOperation) {
          const { resolve, reject } = pendingOperation;
          pendingOperations.current.delete(id);

          if (success) {
            resolve(result);
          } else {
            reject(new Error(error || 'Worker operation failed'));
          }
        }
      };

      workerRef.current.onerror = (error) => {
        console.error('File worker error:', error);
        // Reject all pending operations
        pendingOperations.current.forEach(({ reject }) => {
          reject(new Error('Worker error'));
        });
        pendingOperations.current.clear();
      };

    } catch (error) {
      console.warn('Web Workers not supported, falling back to main thread');
    }

    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
      }
      pendingOperations.current.clear();
    };
  }, []);

  // Generic method to execute worker operations
  const executeOperation = useCallback((operation, data) => {
    return new Promise((resolve, reject) => {
      if (!workerRef.current) {
        reject(new Error('Worker not available'));
        return;
      }

      const id = ++operationId.current;
      pendingOperations.current.set(id, { resolve, reject });

      workerRef.current.postMessage({
        id,
        operation,
        data
      });

      // Set timeout for operation
      setTimeout(() => {
        if (pendingOperations.current.has(id)) {
          pendingOperations.current.delete(id);
          reject(new Error('Operation timeout'));
        }
      }, 30000); // 30 second timeout
    });
  }, []);

  // Search files
  const searchFiles = useCallback(async (files, query, options = {}) => {
    try {
      return await executeOperation('SEARCH', { files, query, options });
    } catch (error) {
      console.warn('Worker search failed, falling back to main thread');
      // Fallback implementation
      const searchQuery = options.caseSensitive ? query : query.toLowerCase();
      return files.filter(file => {
        const fileName = options.caseSensitive ? file.name : file.name.toLowerCase();
        return fileName.includes(searchQuery);
      });
    }
  }, [executeOperation]);

  // Sort files
  const sortFiles = useCallback(async (files, sortBy = 'name', order = 'asc') => {
    try {
      return await executeOperation('SORT', { files, sortBy, order });
    } catch (error) {
      console.warn('Worker sort failed, falling back to main thread');
      // Fallback implementation
      return [...files].sort((a, b) => {
        let aValue = a[sortBy] || a.name;
        let bValue = b[sortBy] || b.name;
        
        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }
        
        if (aValue < bValue) return order === 'asc' ? -1 : 1;
        if (aValue > bValue) return order === 'asc' ? 1 : -1;
        return 0;
      });
    }
  }, [executeOperation]);

  // Filter files
  const filterFiles = useCallback(async (files, filters) => {
    try {
      return await executeOperation('FILTER', { files, filters });
    } catch (error) {
      console.warn('Worker filter failed, falling back to main thread');
      // Fallback implementation
      return files.filter(file => {
        if (filters.types && filters.types.length > 0) {
          return filters.types.includes(file.type);
        }
        return true;
      });
    }
  }, [executeOperation]);

  // Analyze file
  const analyzeFile = useCallback(async (file) => {
    try {
      return await executeOperation('ANALYZE', { file });
    } catch (error) {
      console.warn('Worker analyze failed, falling back to main thread');
      // Fallback implementation
      return {
        wordCount: file.content ? file.content.split(/\s+/).length : 0,
        lineCount: file.content ? file.content.split('\n').length : 0,
        characterCount: file.content ? file.content.length : 0,
        hash: null,
        encoding: 'utf-8',
        language: 'unknown'
      };
    }
  }, [executeOperation]);

  // Compress content
  const compressContent = useCallback(async (content) => {
    try {
      return await executeOperation('COMPRESS', { content });
    } catch (error) {
      console.warn('Worker compress failed, falling back to main thread');
      // Fallback implementation
      return {
        original: content,
        compressed: content,
        originalSize: content.length,
        compressedSize: content.length,
        ratio: 1
      };
    }
  }, [executeOperation]);

  // Generate hash
  const generateHash = useCallback(async (content) => {
    try {
      return await executeOperation('HASH', { content });
    } catch (error) {
      console.warn('Worker hash failed, falling back to main thread');
      // Simple fallback hash
      let hash = 0;
      for (let i = 0; i < content.length; i++) {
        const char = content.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }
      return Math.abs(hash).toString(16);
    }
  }, [executeOperation]);

  // Check if worker is available
  const isWorkerAvailable = useCallback(() => {
    return workerRef.current !== null;
  }, []);

  // Get pending operations count
  const getPendingOperationsCount = useCallback(() => {
    return pendingOperations.current.size;
  }, []);

  return {
    searchFiles,
    sortFiles,
    filterFiles,
    analyzeFile,
    compressContent,
    generateHash,
    isWorkerAvailable,
    getPendingOperationsCount
  };
};
