import { memo, useState, useEffect } from 'react';
import styled from 'styled-components';
import { performanceMonitor, memoryUtils } from '../../utils/performance';
import { Text, Card } from '../../styles/components';

/**
 * Styled components for performance monitor
 */
const MonitorContainer = styled(Card)`
  position: fixed;
  top: ${props => props.theme.spacing.lg};
  right: ${props => props.theme.spacing.lg};
  width: 300px;
  z-index: 9999;
  background: ${props => props.theme.colors.surface}ee;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  font-family: ${props => props.theme.fonts.mono};
  font-size: 0.75rem;
  
  ${props => !props.visible && `
    display: none;
  `}
`;

const MetricRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${props => props.theme.spacing.xs} 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};

  &:last-child {
    border-bottom: none;
  }
`;

const MetricLabel = styled(Text)`
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`;

const MetricValue = styled(Text)`
  color: ${props => props.theme.colors.textSecondary};
  
  ${props => props.warning && `
    color: ${props.theme.colors.warning};
  `}
  
  ${props => props.error && `
    color: ${props.theme.colors.error};
  `}
`;

const ToggleButton = styled.button`
  position: fixed;
  top: ${props => props.theme.spacing.lg};
  right: ${props => props.theme.spacing.lg};
  background: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.borderRadius.sm};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  cursor: pointer;
  font-size: 0.75rem;
  z-index: 10000;
  
  &:hover {
    background: ${props => props.theme.colors.primary}dd;
  }
`;

/**
 * Performance Monitor component for development
 * Shows real-time performance metrics and memory usage
 */
const PerformanceMonitor = memo(({ enabled = false }) => {
  const [visible, setVisible] = useState(false);
  const [metrics, setMetrics] = useState({
    fps: 0,
    memory: null,
    renderTime: 0,
    windowCount: 0,
    componentCount: 0
  });

  // FPS calculation
  useEffect(() => {
    if (!enabled) return;

    let frameCount = 0;
    let lastTime = performance.now();
    let animationId;

    const calculateFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setMetrics(prev => ({
          ...prev,
          fps: Math.round((frameCount * 1000) / (currentTime - lastTime))
        }));
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(calculateFPS);
    };

    calculateFPS();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [enabled]);

  // Memory monitoring
  useEffect(() => {
    if (!enabled) return;

    const updateMemoryInfo = () => {
      const memoryInfo = memoryUtils.getMemoryInfo();
      setMetrics(prev => ({
        ...prev,
        memory: memoryInfo
      }));
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 2000);

    return () => clearInterval(interval);
  }, [enabled]);

  // Component counting (simplified)
  useEffect(() => {
    if (!enabled) return;

    const updateComponentCount = () => {
      // Count DOM elements as a proxy for component count
      const elementCount = document.querySelectorAll('*').length;
      const windowElements = document.querySelectorAll('[data-window]').length;
      
      setMetrics(prev => ({
        ...prev,
        componentCount: elementCount,
        windowCount: windowElements
      }));
    };

    updateComponentCount();
    const interval = setInterval(updateComponentCount, 3000);

    return () => clearInterval(interval);
  }, [enabled]);

  // Render time monitoring
  useEffect(() => {
    if (!enabled) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const renderEntries = entries.filter(entry => 
        entry.entryType === 'measure' && entry.name.includes('render')
      );
      
      if (renderEntries.length > 0) {
        const avgRenderTime = renderEntries.reduce((sum, entry) => 
          sum + entry.duration, 0) / renderEntries.length;
        
        setMetrics(prev => ({
          ...prev,
          renderTime: Math.round(avgRenderTime * 100) / 100
        }));
      }
    });

    try {
      observer.observe({ entryTypes: ['measure'] });
    } catch (e) {
      console.warn('Performance Observer not supported');
    }

    return () => observer.disconnect();
  }, [enabled]);

  // Format memory size
  const formatMemorySize = (bytes) => {
    if (!bytes) return 'N/A';
    const mb = bytes / (1024 * 1024);
    return `${Math.round(mb * 10) / 10} MB`;
  };

  // Get performance status
  const getPerformanceStatus = () => {
    if (metrics.fps < 30) return 'error';
    if (metrics.fps < 50) return 'warning';
    if (metrics.renderTime > 16) return 'warning';
    if (metrics.memory?.usedPercentage > 80) return 'error';
    if (metrics.memory?.usedPercentage > 60) return 'warning';
    return 'good';
  };

  if (!enabled) return null;

  return (
    <>
      <ToggleButton onClick={() => setVisible(!visible)}>
        {visible ? 'Hide' : 'Show'} Perf
      </ToggleButton>
      
      <MonitorContainer visible={visible}>
        <Text size="sm" weight={600} style={{ marginBottom: '0.5rem' }}>
          Performance Monitor
        </Text>
        
        <MetricRow>
          <MetricLabel>FPS</MetricLabel>
          <MetricValue 
            warning={metrics.fps < 50} 
            error={metrics.fps < 30}
          >
            {metrics.fps}
          </MetricValue>
        </MetricRow>
        
        <MetricRow>
          <MetricLabel>Render Time</MetricLabel>
          <MetricValue warning={metrics.renderTime > 16}>
            {metrics.renderTime}ms
          </MetricValue>
        </MetricRow>
        
        <MetricRow>
          <MetricLabel>Windows</MetricLabel>
          <MetricValue warning={metrics.windowCount > 10}>
            {metrics.windowCount}
          </MetricValue>
        </MetricRow>
        
        <MetricRow>
          <MetricLabel>DOM Elements</MetricLabel>
          <MetricValue warning={metrics.componentCount > 1000}>
            {metrics.componentCount}
          </MetricValue>
        </MetricRow>
        
        {metrics.memory && (
          <>
            <MetricRow>
              <MetricLabel>Memory Used</MetricLabel>
              <MetricValue 
                warning={metrics.memory.usedPercentage > 60}
                error={metrics.memory.usedPercentage > 80}
              >
                {formatMemorySize(metrics.memory.usedJSHeapSize)}
              </MetricValue>
            </MetricRow>
            
            <MetricRow>
              <MetricLabel>Memory Total</MetricLabel>
              <MetricValue>
                {formatMemorySize(metrics.memory.totalJSHeapSize)}
              </MetricValue>
            </MetricRow>
            
            <MetricRow>
              <MetricLabel>Memory %</MetricLabel>
              <MetricValue 
                warning={metrics.memory.usedPercentage > 60}
                error={metrics.memory.usedPercentage > 80}
              >
                {Math.round(metrics.memory.usedPercentage)}%
              </MetricValue>
            </MetricRow>
          </>
        )}
        
        <MetricRow>
          <MetricLabel>Status</MetricLabel>
          <MetricValue 
            warning={getPerformanceStatus() === 'warning'}
            error={getPerformanceStatus() === 'error'}
          >
            {getPerformanceStatus().toUpperCase()}
          </MetricValue>
        </MetricRow>
      </MonitorContainer>
    </>
  );
});

PerformanceMonitor.displayName = 'PerformanceMonitor';

export default PerformanceMonitor;
