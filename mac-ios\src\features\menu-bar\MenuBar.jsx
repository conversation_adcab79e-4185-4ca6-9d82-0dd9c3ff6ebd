import React, { memo, useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  RiAppleLine,
  RiWifiLine,
  RiBatteryLine,
  RiVolumeUpLine,
  RiSearchLine
} from 'react-icons/ri';
import { useWindowStore } from '../../store';
import { Text, Flex } from '../../styles/components';
import { glassEffect } from '../../styles/components';

/**
 * Styled components for the menu bar
 */
const MenuBarContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 2rem;
  ${glassEffect}
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 ${props => props.theme.spacing.md};
  font-size: 0.875rem;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
`;

const MenuBarLeft = styled(Flex)`
  align-items: center;
  gap: ${props => props.theme.spacing.md};
`;

const MenuBarRight = styled(Flex)`
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const AppleMenu = styled.div`
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  transition: background-color 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const MenuItems = styled(Flex)`
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  
  @media (max-width: 768px) {
    display: none;
  }
`;

const MenuItem = styled.div`
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  transition: background-color 0.2s ease;
  font-weight: 500;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  ${props => props.active && `
    background: rgba(255, 255, 255, 0.2);
  `}
`;

const StatusItems = styled(Flex)`
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const StatusItem = styled.div`
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs};
  border-radius: ${props => props.theme.borderRadius.sm};
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const TimeDisplay = styled(Text)`
  font-weight: 500;
  font-size: 0.875rem;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  min-width: 4rem;
  text-align: center;
`;

const DateDisplay = styled(Text)`
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  
  @media (max-width: 768px) {
    display: none;
  }
`;

/**
 * MenuBar component that displays the top system menu bar
 * Includes app menus, system status, and time
 */
const MenuBar = memo(() => {
  const { activeWindow, windows } = useWindowStore();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Get active window title for menu display
  const activeWindowTitle = () => {
    if (!activeWindow) return 'Finder';
    const window = windows.find(w => w.id === activeWindow);
    return window ? window.title : 'Finder';
  };

  // Format time for display
  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  // Format date for display
  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle menu item clicks
  const handleMenuClick = (menuItem) => {
    console.log('Menu clicked:', menuItem);
    // TODO: Implement menu functionality
  };

  return (
    <MenuBarContainer>
      <MenuBarLeft>
        <AppleMenu onClick={() => handleMenuClick('apple')}>
          <RiAppleLine size={16} />
        </AppleMenu>
        
        <MenuItems>
          <MenuItem 
            active={true}
            onClick={() => handleMenuClick('app')}
          >
            {activeWindowTitle()}
          </MenuItem>
          <MenuItem onClick={() => handleMenuClick('file')}>
            File
          </MenuItem>
          <MenuItem onClick={() => handleMenuClick('edit')}>
            Edit
          </MenuItem>
          <MenuItem onClick={() => handleMenuClick('view')}>
            View
          </MenuItem>
          <MenuItem onClick={() => handleMenuClick('window')}>
            Window
          </MenuItem>
          <MenuItem onClick={() => handleMenuClick('help')}>
            Help
          </MenuItem>
        </MenuItems>
      </MenuBarLeft>

      <MenuBarRight>
        <StatusItems>
          <StatusItem onClick={() => handleMenuClick('search')}>
            <RiSearchLine size={14} />
          </StatusItem>
          
          <StatusItem onClick={() => handleMenuClick('wifi')}>
            <RiWifiLine size={14} />
          </StatusItem>
          
          <StatusItem onClick={() => handleMenuClick('volume')}>
            <RiVolumeUpLine size={14} />
          </StatusItem>
          
          <StatusItem onClick={() => handleMenuClick('battery')}>
            <RiBatteryLine size={14} />
          </StatusItem>
        </StatusItems>

        <Flex direction="column" align="flex-end">
          <TimeDisplay>
            {formatTime(currentTime)}
          </TimeDisplay>
          <DateDisplay>
            {formatDate(currentTime)}
          </DateDisplay>
        </Flex>
      </MenuBarRight>
    </MenuBarContainer>
  );
});

MenuBar.displayName = 'MenuBar';

export default MenuBar;
